﻿import component from "@/locales/en-US/component";

/**
 * !!! 注意: 这里的hash路由，在 umi 编译时会被统一添加上 ./config.ts 中的 hashPrefix 配置项
 */
export default [
  {
    path: '/',
    component: '../layouts/BasicLayout',
    routes: [
      {
        path: '/chat',
        icon: 'index',
        component: './chat',
        name: '消息',
        key: 'chat',
      },
      // {
      //   name: '私聊',
      //   icon: 'privateMessage',
      //   path: '/exception',
      //   component: './exception/404',
      // },
      // {
      //   name: '活动',
      //   icon: 'activity',
      //   path: '/exception',
      //   component: './exception/404',
      // },
      // {
      //   name: '画板',
      //   icon: 'drawingBoard',
      //   path: '/exception',
      //   component: './exception/404',
      // },
      {
        name: '通讯录',
        icon: 'contact',
        path: '/contact',
        component: './contact',
        key: 'contact',
      },
      // {
      //   path: '/chatele',
      //   component: './chatElectron',
      //   name: 'chat',
      //   icon: 'DashboardOutlined',
      // },
      // {
      //   path: '/dashboard',
      //   name: 'Dashboard',
      //   icon: 'DashboardOutlined',
      //   component: './dashboard/workplace',
      // },
      // {
      //   path: '/list',
      //   icon: 'BorderlessTableOutlined',
      //   name: '列表',
      //   component: './list/table-list',
      // },
      // {
      //   name: '404',
      //   icon: 'WarningOutlined',
      //   path: '/exception',
      //   component: './exception/404',
      // },
      {
        path: '/search',
        icon: '',
        name: '搜索',
        component: './search',
        hideInMenu: true,
        key: 'search'
      },
      {
        path: '/demo',
        icon: '',
        name: '搜索',
        component: './demo',
        hideInMenu: true,
        key: 'search'
      },
      {
        path: '/singleChat',
        icon: '',
        name: '单页聊天',
        component: './singleChat',
        hideInMenu: true,
        key: 'singleChat'
      },
    ],
  },
];

