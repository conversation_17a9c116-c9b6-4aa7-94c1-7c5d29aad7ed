// 引入相关依赖
import dayjs from 'dayjs';

/**
 * 
 * @param currentDate 
 * @returns   当天：显示具体时分 hh:mm；
              昨天：显示 昨天hh:mm;
              7天内：显示  星期几  hh:mm；
              当年内早于7天的： XX月XX日 hh:mm；
              早于当年的就是  XXXX年XX月XX日 
 */
export const getDateContent = (currentDate: Date) => {
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);
  const oneWeekAgo = dayjs().subtract(7, 'day');
  const differenceYears = dayjs().diff(currentDate, 'year');

  if (differenceYears >= 1) {
    return dayjs(currentDate).format('YYYY年MM月DD日 HH:mm');
  } else if (isSameDate(currentDate, today)) {
    return dayjs(currentDate).format('HH:mm');
  } else if (isSameDate(currentDate, yesterday)) {
    return `昨天 ${dayjs(currentDate).format('HH:mm')}`;
  } else if (dayjs(currentDate).isAfter(oneWeekAgo)) {
    // 获取星期几（星期x）
    const weekDays = [
      '星期日',
      '星期一',
      '星期二',
      '星期三',
      '星期四',
      '星期五',
      '星期六',
    ];
    const weekDay = weekDays[currentDate.getDay()];
    return `${weekDay} ${dayjs(currentDate).format('HH:mm')}`;
  } else {
    return dayjs(currentDate).format('MM月DD日 HH:mm');
  }
};
export function isSameDate(date1: Date, date2: Date): boolean {
  return (
    date1.getFullYear() === date2.getFullYear() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getDate() === date2.getDate()
  );
}

export function dateISDifferenceFiveMinutes(
  date1: number,
  date2: number
): boolean {
  return date1 - date2 < 5 * 60 * 1000;
}

/**
 * 判断当前时间是不是本周
 * @param date
 * @returns true/false
 */
export function isCurrentWeek(date: number) {
  const targetTiem = new Date(date);
  // 获取当前日期
  const now = new Date();

  // 获取本周的第一天（周一）的日期
  const firstDayOfWeek = new Date(now);
  firstDayOfWeek.setDate(
    now.getDate() - now.getDay() + (now.getDay() === 0 ? -6 : 1)
  ); // 将周日(0)调整为上周一

  // 获取下周的第一天（周一）的日期
  const firstDayOfNextWeek = new Date(firstDayOfWeek);
  firstDayOfNextWeek.setDate(firstDayOfWeek.getDate() + 7);

  // 重置时间为0点，避免时间部分影响比较
  firstDayOfWeek.setHours(0, 0, 0, 0);
  firstDayOfNextWeek.setHours(0, 0, 0, 0);

  // 检查日期是否在本周范围内
  return targetTiem >= firstDayOfWeek && targetTiem < firstDayOfNextWeek;
}
