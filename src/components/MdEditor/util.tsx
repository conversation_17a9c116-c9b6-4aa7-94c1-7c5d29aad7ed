import type { Command, Transaction, Selection } from '@milkdown/prose/state';
import { TextSelection, Plugin, PluginKey } from '@milkdown/prose/state';
import type { Attrs, NodeType } from '@milkdown/prose/model';
import { EditorView } from '@milkdown/prose/view';
import { Schema } from '@milkdown/prose/model';
import { keymap as createKeymap } from '@milkdown/prose/keymap';
import {
  baseKeymap,
  chainCommands,
  newlineInCode,
  createParagraphNear,
  liftEmptyBlock,
  splitBlock,
} from '@milkdown/prose/commands';
import { splitListItem } from '@milkdown/prose/schema-list';

export function insertTextAtCursor(editorView: EditorView, text: string): void {
  // 获取当前事务
  const { tr } = editorView.state;

  tr.insertText(text);

  // 提交事务
  editorView.dispatch(tr.scrollIntoView());
  editorView.focus();
}

export function clearRange(tr: Transaction) {
  const { $from, $to } = tr.selection;
  const { pos: from } = $from;
  const { pos: to } = $to;
  tr = tr.deleteRange(from - $from.node().content.size, to);
  return tr;
}

export function addBlockType(
  selection: Selection,
  tr: Transaction,
  nodeType: NodeType,
  attrs: Attrs | null = null
) {
  const node = nodeType.createAndFill(attrs);
  if (!node) {
    return null;
  }
  const { $from, $to } = selection;
  return tr.replaceWith($from.before(), $to.after() - 1, node);
}

export function clearContentAndAddBlockType(
  nodeType: NodeType,
  attrs: Attrs | null = null
): Command {
  return (state, dispatch) => {
    const tr = addBlockType(
      state.selection,
      clearRange(state.tr),
      nodeType,
      attrs
    );
    if (!tr) {
      return false;
    }

    if (dispatch) {
      dispatch(tr.setSelection(tr.selection).scrollIntoView());
    }

    return true;
  };
}
export function changeNodeTypePreserveContent(
  nodeType: NodeType,
  attrs: Attrs | null = null
): Command {
  // eslint-disable-next-line max-statements
  return (state, dispatch) => {
    const { selection, tr } = state;
    const { $from, $to } = selection;

    // 如果已经是目标节点类型，则不做任何操作
    if ($from.depth === 0 && $from.parent.type.name === nodeType.name) {
      return false;
    }

    // 辅助函数：创建适当的块，基于节点类型
    const createBlock = (paragraphNodes) => {
      if (nodeType.name === 'ordered_list' || nodeType.name === 'bullet_list') {
        // 对于列表类型，将每个段落包装在列表项中
        const listItems = paragraphNodes.map((paragraph) =>
          state.schema.nodes.list_item.create(null, [paragraph])
        );
        return nodeType.create(attrs, listItems);
      } else {
        // 对于其他类型（如引用块），直接使用段落节点
        return nodeType.create(attrs, paragraphNodes);
      }
    };

    // 情况1：根级文档转换
    if ($from.parent.type.name === 'doc') {
      const children: Node[] = [];
      state.doc.descendants((node) => {
        if (node.type.name === 'paragraph') {
          children.push(node);
        }
      });

      if (children.length > 0) {
        const paragraphNodes = children.map((paragraph) =>
          state.schema.nodes.paragraph.create(null, paragraph.content)
        );

        const block = createBlock(paragraphNodes);
        tr.replaceWith(0, state.doc.content.size, block);

        if (dispatch) {
          dispatch(tr.scrollIntoView());
        }
        return true;
      }
      return false;
    }

    // 情况2：选中了列表，并且要转换为引用块（嵌套处理）
    if (
      nodeType.name === 'blockquote' &&
      ($from.node(1).type.name === 'ordered_list' ||
        $from.node(1).type.name === 'bullet_list')
    ) {
      // 获取整个列表
      const listNode = $from.node(1);
      // 创建一个引用块，将整个列表嵌套在其中
      const blockquote = state.schema.nodes.blockquote.create(attrs, [
        listNode,
      ]);

      // 替换整个列表
      tr.replaceWith($from.before(1), $from.after(1), blockquote);

      if (dispatch) {
        dispatch(tr.scrollIntoView());
      }
      return true;
    }

    // 情况3：选中了多个段落
    if (
      $from.parent.type.name === 'paragraph' &&
      $to.parent.type.name === 'paragraph' &&
      $from.pos !== $to.pos
    ) {
      const paragraphs: Node[] = [];
      state.doc.nodesBetween($from.pos, $to.pos, (node) => {
        if (node.type.name === 'paragraph') {
          paragraphs.push(node);
        }
      });

      if (paragraphs.length > 1) {
        const paragraphNodes = paragraphs.map((paragraph) =>
          state.schema.nodes.paragraph.create(null, paragraph.content)
        );

        const block = createBlock(paragraphNodes);
        tr.replaceWith($from.before(), $to.after(), block);

        if (dispatch) {
          dispatch(tr.scrollIntoView());
        }
        return true;
      }
    }

    // 情况4：选择跨越了不同类型的节点（新增处理）
    if ($from.parent.type.name !== $to.parent.type.name) {
      // 收集选区内的所有内容
      const fragments = [];
      state.doc.nodesBetween($from.pos, $to.pos, (node) => {
        if (node.isTextblock) {
          fragments.push(node);
        }
      });

      if (fragments.length > 0) {
        // 将所有内容转换为段落
        const paragraphNodes = fragments.map((fragment) =>
          state.schema.nodes.paragraph.create(null, fragment.content)
        );

        const block = createBlock(paragraphNodes);
        tr.replaceWith($from.before(), $to.after(), block);

        if (dispatch) {
          dispatch(tr.scrollIntoView());
        }
        return true;
      }
    }

    // 情况5：非段落节点选中
    if ($from.parent.type.name !== 'paragraph') {
      return clearContentAndAddBlockType(nodeType, attrs)(state, dispatch);
    }

    // 情况6：嵌套在列表中
    if ($from.depth > 1) {
      const rootNode = $from.node(1);
      if (
        rootNode.type.name === 'ordered_list' ||
        rootNode.type.name === 'bullet_list'
      ) {
        const node = nodeType.createAndFill(attrs);
        if (!node) {
          return false;
        }
        tr.replaceSelectionWith(node);
        if (dispatch) {
          dispatch(
            tr
              .setSelection(
                TextSelection.create(tr.doc, $from.pos + node.nodeSize - 2)
              )
              .scrollIntoView()
          );
        }
        return true;
      }
    }

    // 情况7：单个段落转换
    const currentContent = $from.parent.content;

    // 创建一个具有相同内容但新节点类型的节点
    let newNode;
    if (nodeType.name === 'ordered_list' || nodeType.name === 'bullet_list') {
      // 对于列表节点，将内容包装在列表项中
      const paragraph = state.schema.nodes.paragraph.create(
        null,
        currentContent
      );
      const listItem = state.schema.nodes.list_item.create(null, [paragraph]);
      newNode = nodeType.create(attrs, [listItem]);
    } else if (nodeType.name === 'blockquote') {
      const paragraph = state.schema.nodes.paragraph.create(
        null,
        currentContent
      );
      newNode = nodeType.create(attrs, [paragraph], $from.parent.marks);
    } else {
      // 对于其他节点，直接使用内容
      newNode = nodeType.create(attrs, currentContent, $from.parent.marks);
    }

    if (!newNode) {
      console.warn('创建新节点失败');
      return false;
    }

    // 用新节点替换当前节点
    tr.replaceWith($from.before(), $to.after() - 1, newNode);

    if (dispatch) {
      dispatch(tr.scrollIntoView());
    }

    return true;
  };
}

const splitListItemCom = (state, dispatch) => {
  return splitListItem(state.schema.nodes.list_item)(state, dispatch);
};

/*
 * 重写 baseKeymap
 * 添加 Ctrl-Enter 快捷键
 */

function overrideBaseKeymap(keymap: Record<string, Command>) {
  const handleEntry = () => {
    return true;
  };
  keymap.Enter = handleEntry;
  keymap['Ctrl-Enter'] = chainCommands(
    newlineInCode,
    splitListItemCom,
    createParagraphNear,
    liftEmptyBlock,
    splitBlock
  );
  keymap['Shift-Enter'] = chainCommands(
    newlineInCode,
    splitListItemCom,
    createParagraphNear,
    liftEmptyBlock,
    splitBlock
  );
  return keymap;
}
export const customKeymap = createKeymap(overrideBaseKeymap(baseKeymap));

export const pasteKey = new PluginKey('MILKDOWN_LISTENER');
// 响应粘贴事件
export const pastePlugin = (
  fn: (view: EditorView, event: ClipboardEvent) => boolean
) => {
  return new Plugin({
    key: pasteKey,
    props: {
      handleDOMEvents: {
        paste(view, event) {
          fn(view, event);
        },
      },
    },
  });
};
