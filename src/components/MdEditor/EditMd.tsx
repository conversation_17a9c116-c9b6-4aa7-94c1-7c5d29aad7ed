/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-statements */
/* eslint-disable max-lines */

import type { Ctx } from '@milkdown/ctx';
import { Node, Slice } from '@milkdown/prose/model';

import {
  editorStateOptionsCtx,
  prosePluginsCtx,
  schemaCtx,
  CmdKey,
  commandsCtx,
  editorViewCtx,
} from '@milkdown/core';

import { EditorState, Plugin } from '@milkdown/prose/state';
import React, {
  useEffect,
  useRef,
  forwardRef,
  useImperativeHandle,
  useContext,
  useState,
} from 'react';
import classNames from 'classnames';
import { Crepe, CrepeFeature } from '@/components/Crepe';
import { EditorView } from '@milkdown/prose/view';
import { replaceAll } from '@milkdown/utils';
import {
  toggleStrongCommand,
  toggleEmphasisCommand,
  // linkSchema,
  blockquoteSchema,
  bulletListSchema,
  codeBlockSchema,
  orderedListSchema,
  toggleInlineCodeCommand,
  textSchema,
  htmlSchema,
} from '@milkdown/preset-commonmark';
import { linkTooltipAPI } from '@/components/Crepe/components/link-tooltip';
import { Tooltip } from '@ht/sprite-ui';

import {
  strikethroughSchema,
  toggleStrikethroughCommand,
} from '@milkdown/kit/preset/gfm';
// import '@ht/milkdown-crepe/lib/theme/common/style.css';
// import '@ht/milkdown-crepe/lib/theme/frame/style.css';

import boldIcon from '@/assets/channel/messageInput/menu/bold.svg';
import italicIcon from '@/assets/channel/messageInput/menu/italic.svg';
import throughIcon from '@/assets/channel/messageInput/menu/through.svg';
import linkIcon from '@/assets/channel/messageInput/menu/link.svg';
import orderIcon from '@/assets/channel/messageInput/menu/order.svg';
import disorderIcon from '@/assets/channel/messageInput/menu/disorder.svg';
import replyIcon from '@/assets/channel/messageInput/menu/reply.svg';
import { useWidgetViewFactory } from '@prosemirror-adapter/react';

import { GroupMemberItem } from '@ht/openim-wasm-client-sdk';
import { useDeepCompareEffect } from 'ahooks';
import { getMarkdownFromJson } from '@/utils/parserMdToHtml';
import { EditorContext } from './editorContent';
import {
  pastePlugin,
  customKeymap,
  changeNodeTypePreserveContent,
  insertTextAtCursor,
} from './util';
import styles from './index.less';
import { MentionsPluginDropdownView } from './plugin-mention/MentionDrop';
import { MentionsPlugin, MentionsPluginOptions } from './plugin-mention';
import { MentionsOptions } from './plugin-mention/plugin';
import { linkSchema } from './plugin-link';

interface EditMdProps {
  groupMemberList?: GroupMemberItem[];
  showFormater?: boolean;
  defaultValue?: string;
  defaultValueType?: 'nodeType' | 'markdownType';
  flush?: boolean; // flush is true, the editor state will be re-created.
  inputStyle?: any;
  onChange?: (value: string) => void;
  onFocus?: (ctx: Ctx) => void;
  onBlur?: (ctx: Ctx) => void;
  onError?: (error: Error) => void;
  onPaste?: (view: EditorView, event: ClipboardEvent) => boolean;
  onCompositionStart?: () => void;
  onCompositionEnd?: () => void;
  onKeyDown?: (e: any) => void;
  onMention?: (value: GroupMemberItem) => void;
  onMentionRemove?: (item: { id: string; name: string }) => void;
  hideBtn?: boolean;
  disabledBtn?: boolean;
}
export const FEATURES = {
  [CrepeFeature.Cursor]: false,
  [CrepeFeature.ListItem]: true,
  [CrepeFeature.LinkTooltip]: true,
  [CrepeFeature.ImageBlock]: true,
  [CrepeFeature.BlockEdit]: false,
  [CrepeFeature.Placeholder]: false,
  [CrepeFeature.Toolbar]: false,
  [CrepeFeature.CodeMirror]: true,
  [CrepeFeature.Table]: false,
  [CrepeFeature.Latex]: false,
};
export const featureConfigs = {
  // [CrepeFeature.CodeMirror]: {
  //   // 配置CodeMirror主题
  //   theme: [materialDark],
  //   extensions: [material, javascript({ jsx: true })],
  // },
  [CrepeFeature.LinkTooltip]: {
    inputPlaceholder: '输入链接',
  },
};
/**
 * Markdown Editor Component
 */
const EditMdComponent = (
  {
    groupMemberList,
    showFormater = true,
    defaultValue = '',
    defaultValueType = 'nodeType',
    flush = false,
    inputStyle = {},
    onChange,
    onError,
    onFocus,
    onBlur,
    onPaste,
    onCompositionStart,
    onCompositionEnd,
    onKeyDown,
    onMention,
    onMentionRemove = (node) => {},
    hideBtn = false,
    disabledBtn = false,
  }: EditMdProps,
  ref: any
) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const { editorInstance, setEditorInstance } = useContext(EditorContext);
  const widgetViewFactory = useWidgetViewFactory();
  const mentions = MentionsPlugin(widgetViewFactory);
  const [activeMarks, setActiveMarks] = useState<Set<string>>(new Set());
  // const { editor, setEditor } = useContext();

  useDeepCompareEffect(() => {
    if (!containerRef.current) {
      return;
    } else if (containerRef.current == null && defaultValue == null) {
      return;
    }

    let defaultValueTemp = defaultValue;
    try {
      defaultValueTemp = getMarkdownFromJson(defaultValue);
    } catch {}
    const crepeInstance = new Crepe({
      root: containerRef.current,
      defaultValue: '',
      features: FEATURES,
      featureConfigs,
    });

    crepeInstance.editor
      .config((ctx) => {
        ctx.update(MentionsPluginOptions.key, (prev: MentionsOptions) => {
          return {
            ...prev,
            list: [],
            view: MentionsPluginDropdownView,
            onMention,
          };
        });
      })
      .use(mentions);

    crepeInstance
      .create()
      .then(() => {
        setEditorInstance(crepeInstance);
        const editorView = crepeInstance.editor.ctx.get(editorViewCtx);

        // 在这里直接设置 defaultValue
        if (defaultValue) {
          try {
            // markdwonType 不支持@粘贴
            if (defaultValueType === 'nodeType') {
              replaceAllForDraft(defaultValue, false)(crepeInstance.editor.ctx);
            } else {
              replaceAll(defaultValue, false)(crepeInstance.editor.ctx);
            }
          } catch (e) {
            console.error('Failed to replace defaultValue:', e);
          }
        }

        editorView.dom.addEventListener(
          'compositionstart',
          handleCompositionStart
        );
        editorView.dom.addEventListener('compositionend', handleCompositionEnd);
        // editorView.dom.addEventListener('selectionchange', () => {
        //   updateActiveMarks(crepeInstance.editor.ctx);
        // });
        editorView.updateState(
          editorView.state.reconfigure({
            plugins: [
              customKeymap,
              // mentionRemovePlugin(onMentionRemove),
              ...(!handlePaste ? [] : [pastePlugin(handlePaste)]),
              ...editorView.state.plugins,
              new Plugin({
                view: () => ({
                  update: (view, prevState) => {
                    const { state } = view;
                    if (prevState && prevState.selection !== state.selection) {
                      updateActiveMarks(crepeInstance.editor.ctx);
                    }
                  },
                }),
              }),
            ],
          })
        );

        // 初始化完成立刻focus
        editorView.focus();

        crepeInstance.on((listener) => {
          listener.markdownUpdated((ctx, markdown, preMarkDown) => {
            //
            onChange?.(markdown);
            updateActiveMarks(ctx);
          });
          if (onFocus) {
            listener.focus(onFocus);
          }
          if (onBlur) {
            listener.blur(onBlur);
          }
          listener.destroy((ctx) => {
            editorView.dom.removeEventListener(
              'compositionstart',
              handleCompositionStart
            );
            editorView.dom.removeEventListener(
              'compositionend',
              handleCompositionEnd
            );
          });
        });
      })
      .catch((error) => {
        console.error('Failed to initialize Milkdown editor:', error);
        onError?.(error);
      });

    if (disabledBtn) {
      crepeInstance.setReadonly(true);
    }

    return () => {
      if (editorInstance) {
        editorInstance.destroy();
        setEditorInstance(null);
      }
      crepeInstance?.destroy();
    };
  }, [disabledBtn, defaultValue, defaultValueType]);

  useEffect(() => {
    if (!editorInstance) {
      return;
    }

    editorInstance.editor.action((ctx) => {
      ctx.update(MentionsPluginOptions.key, (prev) => ({
        ...prev,
        list: groupMemberList,
      }));
    });
  }, [groupMemberList, editorInstance]);

  useEffect(() => {
    const handleGlobalClick = (e: MouseEvent) => {
      const linkEditElement = document.querySelector('.link-edit');
      if (!linkEditElement || !editorInstance?.editor) {
        return;
      }

      const { parentElement } = linkEditElement;
      const isClickInside =
        linkEditElement.contains(e.target as Node) ||
        (e.target as HTMLElement).classList.contains('milkdown'); // preview时点击编辑时，不应该清除

      const shouldRemove = parentElement?.dataset?.show === 'true';

      if (!isClickInside && shouldRemove) {
        removeLink();
      }
    };

    document.addEventListener('click', handleGlobalClick);
    return () => {
      document.removeEventListener('click', handleGlobalClick);
    };
  }, [editorInstance]);

  const replaceAllForDraft = (markdown: string, flush = false) => {
    return (ctx: Ctx): void => {
      try {
        const view = ctx.get(editorViewCtx);
        const schema = ctx.get(schemaCtx);
        const options = ctx.get(editorStateOptionsCtx);
        const plugins = ctx.get(prosePluginsCtx);
        const doc = Node.fromJSON(schema, JSON.parse(markdown));
        if (!doc) {
          return;
        }

        if (!flush) {
          const { state } = view;

          return view.dispatch(
            state.tr.replace(
              0,
              state.doc.content.size,
              new Slice(doc.content, 0, 0)
            )
          );
        }

        const state = EditorState.create({
          schema,
          doc,
          plugins,
          ...options,
        });

        view.updateState(state);
      } catch (e) {
        console.error('replaceAllForDraft', e);
      }
    };
  };

  const updateActiveMarks = (ctx) => {
    if (!ctx) {
      return;
    }

    const view = ctx.get(editorViewCtx);
    const { state } = view;
    const { selection, doc, schema } = state;
    const marks = new Set<string>();

    if (selection.empty) {
      // 获取光标位置的标记
      const storedMarks = selection.$from.marks();
      storedMarks.forEach((mark) => {
        if (mark.type === schema.marks.strong) {
          marks.add('bold');
        }
        if (mark.type === schema.marks.emphasis) {
          marks.add('italic');
        }
        if (mark.type === schema.marks.strike_through) {
          marks.add('strikethrough');
        }
        if (mark.type === schema.marks.link) {
          marks.add('link');
        }
        if (mark.type === schema.marks.code) {
          marks.add('code');
        }
      });
    } else {
      // 处理选中文本的情况 - 检查选中范围内是否有这些标记
      const { from, to } = selection;
      for (let pos = from; pos <= to; pos++) {
        const node = doc.nodeAt(pos);
        if (node) {
          node.marks.forEach((mark) => {
            if (mark.type === schema.marks.strong) {
              marks.add('bold');
            }
            if (mark.type === schema.marks.emphasis) {
              marks.add('italic');
            }
            if (mark.type === schema.marks.strike_through) {
              marks.add('strikethrough');
            }
            if (mark.type === schema.marks.link) {
              marks.add('link');
            }
            if (mark.type === schema.marks.code) {
              marks.add('code');
            }
          });
        }
      }
    }

    const { $from } = state.selection;
    for (let d = $from.depth; d >= 0; d--) {
      const node = $from.node(d);
      if (node.type === schema.nodes.ordered_list) {
        marks.add('order');
      }
      if (node.type === schema.nodes.bullet_list) {
        marks.add('disorder');
      }
      if (node.type === schema.nodes.blockquote) {
        marks.add('reply');
      }
      if (node.type === schema.nodes.code_block) {
        marks.add('codeBlock');
      }
    }

    setActiveMarks(marks);
  };
  const handlePaste = (view, event) => {
    const items = Array.from(event.clipboardData?.items || []);
    const hasImage = items.some((item) => item.type.startsWith('image'));
    if (hasImage) {
      event.preventDefault(); // 阻止粘贴图片
      return true;
    }

    onPaste?.(view, event);
    return false;
  };

  const handleCompositionStart = () => {
    onCompositionStart?.();
  };
  const handleCompositionEnd = () => {
    onCompositionEnd?.();
  };

  useImperativeHandle(
    ref,
    () => ({
      clearValue,
      focus: jsToMdFocus,
      getValue,
      insertEmoji,
      getMentionList,
      getNodeJson,
    }),
    [editorInstance]
  );

  const getNodeJson = () => {
    const { state } = editorInstance?.editor?.ctx?.get(editorViewCtx) || {};
    const { doc } = state; // ProseMirror Node

    if (doc != null) {
      const json = doc.toJSON();
      return JSON.stringify(json);
    }
    return null;
  };
  const handle = (key: CmdKey<unknown>) => {
    if (!editorInstance?.editor) {
      return;
    }

    try {
      const { editor } = editorInstance;
      const commands = editor.ctx.get(commandsCtx);

      commands.call(key);
    } catch (error) {
      console.error('Failed:', error);
    }
  };
  const handleLink = () => {
    if (!editorInstance?.editor) {
      return;
    }
    const { editor } = editorInstance;
    const view = editor.ctx.get(editorViewCtx);
    const { selection } = view.state;
    const {
      state: { doc },
    } = view;
    // If the selection has a link mark, remove it.
    const hasLinkMark = selection.$from
      .marks()
      .some((mark) => mark.type === linkSchema.type(editor.ctx));

    if (hasLinkMark) {
      removeLink();
      return;
    }

    editor.ctx.get(linkTooltipAPI.key).addLink(selection.from, selection.to);
  };
  const removeLink = () => {
    if (!editorInstance?.editor) {
      return;
    }

    const { editor } = editorInstance;
    const view = editor.ctx.get(editorViewCtx);
    const { selection } = view.state;

    editor.ctx.get(linkTooltipAPI.key).removeLink(selection.from, selection.to);
  };
  const nodeHandle = (type: any) => {
    if (!editorInstance?.editor) {
      return;
    }

    try {
      const { editor } = editorInstance;
      const view = editor.ctx.get(editorViewCtx);
      const { dispatch, state } = view;

      const command = changeNodeTypePreserveContent(type(editor.ctx));
      command(state, dispatch);
      view.focus();
    } catch (error) {
      console.error('Failed', error);
    }
  };
  // 插入表情
  const insertEmoji = (emoji: string) => {
    if (!editorInstance?.editor) {
      return;
    }
    const { editor } = editorInstance;
    const view = editor.ctx.get(editorViewCtx);
    insertTextAtCursor(view, emoji);
  };

  const textOperateList = [
    {
      idx: 0,
      title: '加粗',
      icon: boldIcon,
      type: 'bold',
      onClick: () => {
        handle(toggleStrongCommand.key);
      },
    },
    {
      idx: 1,
      title: '倾斜',
      icon: italicIcon,
      type: 'italic',
      onClick: () => {
        handle(toggleEmphasisCommand.key);
      },
    },
    {
      idx: 2,
      title: '删除线',
      icon: throughIcon,
      type: 'strikethrough',
      onClick: () => {
        handle(toggleStrikethroughCommand.key);
      },
    },
    {
      type: 'divider',
    },
    {
      idx: 3,
      title: '链接',
      icon: linkIcon,
      type: 'link',
      onClick: () => {
        handleLink();
      },
    },
    {
      idx: 4,
      title: '有序列表',
      icon: orderIcon,
      type: 'order',
      onClick: () => {
        nodeHandle(orderedListSchema.type);
      },
    },
    {
      idx: 5,
      title: '无序列表',
      icon: disorderIcon,
      type: 'disorder',
      onClick: () => {
        nodeHandle(bulletListSchema.type);
      },
    },
    {
      type: 'divider',
    },
    {
      idx: 6,
      title: '引用',
      icon: replyIcon,
      type: 'reply',
      onClick: () => {
        nodeHandle(blockquoteSchema.type);
      },
    },
    // {
    //   idx: 7,
    //   title: '代码块',
    //   icon: codeIcon,
    //   onClick: () => {
    //     nodeHandle(codeBlockSchema.type);
    //   },
    // },
    // {
    //   idx: 8,
    //   title: '代码',
    //   icon: codeTagIcon,
    //   onClick: () => {
    //     handle(toggleInlineCodeCommand.key);
    //   },
    // },
  ];

  // 清楚当前编辑内容
  const clearValue = () => {
    if (!editorInstance?.editor) {
      return;
    }
    try {
      const { editor } = editorInstance;
      const view = editor.ctx.get(editorViewCtx);
      const { tr } = view.state;
      tr.replaceWith(
        0,
        view.state.doc.content.size,
        view.state.schema.topNodeType.createAndFill() || []
      );
      view.dispatch(tr);
    } catch (error) {
      console.error('clearValue', error);
    }
  };

  const jsToMdFocus = () => {
    if (!editorInstance?.editor) {
      return;
    }
    try {
      const { editor } = editorInstance;
      const view = editor.ctx.get(editorViewCtx);
      view.focus();
    } catch (error) {
      console.error('editMdFocus', error);
    }
  };

  const editMdFocus = (e: React.MouseEvent<HTMLElement>): void => {
    // 获取目标元素
    const target = e?.target as HTMLElement;
    if (!target || !editorInstance?.editor) {
      return;
    }

    // 检查目标元素及其所有父元素是否包含特定类名
    let currentElement: HTMLElement | null = target;

    while (currentElement) {
      if (
        currentElement.classList?.contains('input-area') ||
        currentElement.tagName.toLowerCase() === 'milkdown-code-block-custom'
      ) {
        return;
      }
      currentElement = currentElement.parentElement;
    }

    try {
      const { editor } = editorInstance;
      const view = editor.ctx.get(editorViewCtx);
      view.focus();
    } catch (error) {
      console.error('editMdFocus:', error);
    }
  };

  const getValue = () => {
    const value = editorInstance?.getMarkdown();
    return value || '';
  };
  const getMentionList = () => {
    const mentions: [string] | [] = [];
    const { state } = editorInstance.editor.ctx.get(editorViewCtx) || {};
    if (state) {
      state.doc.descendants((node: any) => {
        if (node.type.name === 'mention') {
          mentions.push(node.attrs.id);
        }
      });
    }
    return mentions || '';
  };

  return (
    <div className={styles.editorContainer} onClick={editMdFocus}>
      <div
        className={styles.textOperate}
        style={showFormater && !hideBtn ? {} : { display: 'none' }}
        onClick={(e) => e.stopPropagation()}
      >
        {textOperateList.map((item, index) => {
          if (item.type && item.type === 'divider') {
            // eslint-disable-next-line react/no-array-index-key
            return <div key={`${index}divider`} className={styles.line}></div>;
          } else {
            const isActive = activeMarks.has(item.type);
            return (
              <div
                key={item.idx}
                onClick={() => {
                  if (item.onClick && !disabledBtn) {
                    item?.onClick();
                  }
                }}
                onMouseDown={(e) => e.preventDefault()}
                className={classNames(
                  styles.operateItem,
                  disabledBtn && styles.operateItemdisabled,
                  isActive && styles.activeIcon
                )}
              >
                <div
                  className={classNames(
                    disabledBtn && styles.operateItemdisabled
                  )}
                >
                  <Tooltip
                    title={item.title ? item.title : null}
                    overlayClassName={styles.tooltipWrap}
                  >
                    <img src={item.icon} alt={item.title} />
                  </Tooltip>
                </div>
              </div>
            );
          }
        })}
      </div>
      <div
        ref={containerRef}
        className={styles.inputWrap}
        style={{
          ...inputStyle,
          maxHeight: showFormater && !hideBtn ? 'calc(100% - 40px)' : '100%',
        }}
      ></div>
    </div>
  );
};

const EditMd = forwardRef(EditMdComponent);
export default EditMd;
