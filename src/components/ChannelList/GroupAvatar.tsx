import React, { useMemo } from 'react';
import groupIcon from '@/assets/coversation/group.svg';
import { getAvatarUrl, getRandomDefaultAvatar } from '@/utils/avatar';
import classNames from 'classnames';
import { useUserStore } from '@/store';
import styles from './index.less';

interface GroupAvatarProps {
  faceMember?: string;
  size?: 'small' | 'little';
  style: any;
}

const GroupAvatar: React.FC<GroupAvatarProps> = ({
  faceMember,
  size,
  style,
}) => {
  const { selfInfo } = useUserStore();
  const groupMemberList = useMemo(() => {
    if (!faceMember) {
      return null;
    }
    try {
      const parsed = JSON.parse(faceMember);
      return Array.isArray(parsed) && parsed.length > 0 ? parsed : null;
    } catch {
      return null;
    }
  }, [faceMember]);

  const count = Math.min(groupMemberList?.length || 0, 9);

  if (!groupMemberList || count === 0) {
    return (
      <div
        style={style}
        className={classNames(
          styles.avatarContainer,
          size != null && styles[size]
        )}
      >
        <img src={groupIcon} className={styles.avatarTotal0} />
      </div>
    );
  }

  if (count === 1) {
    const avatarSrc = groupMemberList[0];
    return (
      <div
        style={style}
        className={classNames(
          styles.avatarContainer,
          size != null && styles[size]
        )}
      >
        <img
          src={getAvatarUrl(avatarSrc)}
          onError={(e) => {
            (e.target as HTMLImageElement).src =
              getRandomDefaultAvatar(avatarSrc);
          }}
          className={styles.avatarTotal1}
        />
      </div>
    );
  }

  const layoutConfigs: { [key: number]: number[] } = {
    2: [2],
    3: [1, 2], // 3 avatars: 1 on top, 2 on bottom
    4: [2, 2],
    5: [2, 3], // 5 avatars: 2 on top, 3 on bottom
    6: [3, 3],
    7: [1, 3, 3], // 7 avatars: 1 on top, 3 in middle, 3 on bottom
    8: [2, 3, 3], // 8 avatars: 2 on top, 3 in middle, 3 on bottom
    9: [3, 3, 3],
  };

  const currentLayout = layoutConfigs[count];

  if (!currentLayout) {
    return null;
  }

  const avatarMembers = [...groupMemberList];

  return (
    <div
      style={style}
      className={classNames(
        styles.avatarContainer,
        size != null && styles[size]
      )}
    >
      <div className={styles.verticalBox} style={{ margin: '1px 0px 0px 1px' }}>
        {currentLayout.map((avatarsInRow, rowIndex) => (
          <div key={`row-${rowIndex}`} className={styles.horizontalBox}>
            {Array.from({ length: avatarsInRow }).map((_, colIndex) => {
              const member = avatarMembers.shift();
              if (!member) {
                return null;
              }

              const globalAvatarIndex = groupMemberList.indexOf(member);
              const className =
                styles[`avatarTotal${count}_${globalAvatarIndex + 1}`] ||
                styles[`avatarTotal${count}`];

              return (
                <img
                  key={`avatar-${globalAvatarIndex}`}
                  src={
                    selfInfo?.userID === member
                      ? selfInfo.faceURL || getAvatarUrl(selfInfo.userID)
                      : getAvatarUrl(member)
                  }
                  onError={(e) => {
                    (e.target as HTMLImageElement).src =
                      getRandomDefaultAvatar(member);
                  }}
                  className={className}
                  alt={`Avatar ${globalAvatarIndex + 1}`}
                />
              );
            })}
          </div>
        ))}
      </div>
    </div>
  );
};

export default GroupAvatar;
