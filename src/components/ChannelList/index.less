.channelListWrapper {
  background: var(--primary-background-color-17);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
  margin-right: 10px;

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 14px 14px 0 20px;
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-text-color-1);

    .headerTitle {
      margin-right: 8px;
      line-height: 24px;
    }
    .headerAddIcon {
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      width: 32px;
      height: 32px;
      &:hover {
        background: rgba(107, 107, 108, 8%);
        border-radius: 6px;
      }
      img {
        width: 20px;
      }
    }
  }

  :global .linkflow-menu {
    background-color: var(--primary-background-color);
  }

  .listarea {
    padding-top: 10px;
    flex: 1;
    overflow: auto;
    scrollbar-width: none;
    scrollbar-color: transparent transparent;
    display: flex;
    flex-direction: column;

    &::-webkit-scrollbar {
      display: none;
    }

    .channelItemListWrapper {
      height: 100%;
      overflow-y: auto;
      padding-bottom: 10px;
      scrollbar-width: none;
      scrollbar-color: transparent transparent;
      &::-webkit-scrollbar {
        display: none;
      }

      &::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 20%);
        border-radius: 3px;
      }

      &::-webkit-scrollbar-track {
        background-color: transparent;
      }

      // /* 滚动条按钮 (上下箭头) */
      &::-webkit-scrollbar-button {
        display: none; /* 隐藏按钮 */
      }

      // max-height: 800px;
      transition: max-height 0.1s ease, visibility 0.5s ease, opacity 0.3s ease;
      opacity: 1;
      visibility: visible;
      position: relative;
      &.hidden {
        opacity: 0;
        visibility: hidden;
        max-height: 0;
      }

      .threadContainer {
        padding: 0 8px;
        position: relative;

        .threadBox {
          display: flex;
          align-items: center;
          padding: 12px;

          .iocnBox {
            margin: 0 11px 0 0;
            position: relative;

            .unreadCount {
              position: absolute;
              top: -7px;
              right: -7px;
              width: 16px;
              height: 16px;
              border-radius: 50%;
              background: #d6363f;
              color: #fff;
              font-size: 10px;
              text-align: center;
            }

            .redIcon {
              position: absolute;
              top: -6px;
              right: -6px;
              width: 12px;
              height: 12px;
            }
          }

          .rightContent {
            flex: 1;
            overflow: hidden;

            .headerContainer {
              display: flex;
              align-items: center;
              justify-content: space-between;
              transform: translateY(-1px);

              .showName {
                font-size: 15px;
                font-weight: 600;
                color: #323338;
                line-height: 21px;
              }

              .notNotifyIcon {
                margin-left: 8px;
              }
            }

            .descContent {
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin-top: 1px;
              font-family: PingFangSC-Regular, "lato", EmojiMart;

              .contentDesc {
                font-size: 13px;
                font-weight: 400;
                color: var(--primary-text-color-7);
                line-height: 20px;
                height: 20px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                display: flex;

                .mentionedTag,
                .draftTag {
                  color: #d6363f;
                }

                .mdContent {
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }
              }

              .time {
                margin-left: 6px;
                font-size: 12px;
                font-weight: 400;
                color: var(--primary-text-color-7);
                line-height: 17px;
                word-break: keep-all;
              }
            }
          }
        }

        .state {
          height: 20px;
          line-height: 20px;
          display: flex;
          align-items: center;
          img {
            width: 18px;
            height: 18px;
          }
        }

        .popStateContainer {
          :global {
            .linkflow-popover-content {
              border-radius: 5px;
              // background: black;
              color: #fff !important;
            }
            .linkflow-popover-inner-content {
              border-radius: 5px;
              background: black;
              padding: 6px 10px !important;
              color: #fff !important;
            }
            .linkflow-popover-arrow-content::before {
              background: black;
            }
          }
        }

        &.isPinned {
          .threadBox {
            background: rgba(112, 158, 244, 10%);
            border-radius: 0;
          }

          &:first-of-type {
            .threadBox {
              border-radius: 4px 4px 0 0;
            }
          }

          &::before {
            content: "";
            display: block;
            width: calc(100% - 16px);
            height: 1px;
            background: rgba(112, 158, 244, 10%);
            position: absolute;
            bottom: 0;
            left: 8px;
          }
        }

        &.isLastPinned {
          .threadBox {
            border-radius: 0 0 4px 4px;
          }
          &::before {
            display: none;
          }
        }

        &:hover {
          .threadBox {
            background: rgba(107, 107, 108, 8%);
            border-radius: 4px !important;
            cursor: pointer;
          }
        }

        &.active {
          .threadBox {
            background: var(--tab-actived-background-color);
            border-radius: 4px !important;
          }
        }

        &::after {
          content: "";
          display: block;
          width: calc(100% - 16px);
          height: 1px;
          background: rgba(0, 0, 0, 5%);
          position: relative;
          bottom: 0;
          left: 8px;
        }

        // 子群相关样式
        .threadItems {
          margin-top: 4px;
          margin-bottom: 4px;
          cursor: pointer;
        }

        .threadItem {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          min-height: auto;
          margin-left: 9px;
          height: 28px;
        }

        .lineContainer {
          position: absolute;
          left: 8px;
          width: 17px;
          display: flex;
          align-items: center;
          height: 100%;
          overflow: hidden;
        }

        .verticalLine {
          position: absolute;
          left: 0;
          border-left: 2px solid var(--link-color-tree-base);
        }

        .horizontalLine {
          position: absolute;
          width: 17px;
          border-bottom: 2px solid var(--link-color-tree-base);
          border-left: 2px solid var(--link-color-tree-base);
          display: block;
          top: 50%;
          left: 0;
          height: 8px;
          transform: translateY(-50%);
        }

        .threadContent {
          margin-left: 31px;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 14px;
          font-weight: 400;
          display: flex;
          align-items: center;
          flex: 1;
          line-height: 20px;
          overflow: hidden;

          .threadName {
            flex: 1;
            display: flex;
            align-items: center;
            // height: 28px;
            line-height: 20px;
            height: 20px;
            overflow: hidden;
            span {
              font-size: 14px;
              font-weight: 400;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              color: var(--list-text-color-soft);
            }
          }
          &:hover {
            height: 28px;
            background-color: #cad8e6;
            border-radius: 6px;
            span {
              color: var(--list-text-color-soft);
            }
          }
          &.highlighted,
          &.highlighted:hover {
            height: 28px;
            background: var(--link-color-theme-base-hgl-1);
            border-radius: 6px;
            span {
              color: var(--link-color-theme-content-hgl-1) !important;
            }
          }
        }
      }
    }

    .listEmptyDesc {
      font-size: 14px;
      color: #999ba0;
      line-height: 20px;
      height: 100%;
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;

      > img {
        margin-bottom: 14px;
      }
    }
  }
}

.dropDownMenu,
.menu {
  :global {
    .linkflow-dropdown-menu.linkflow-dropdown-menu-root,
    .linkflow-dropdown-menu {
      -webkit-user-select: none;
      user-select: none;
      border-radius: 8px;
      width: 220px;
      min-width: 200px;
      max-width: 100%;
      overflow: hidden;
      padding: 6px;
      border: 1px solid var(--primary-background-color-5);
    }

    .linkflow-dropdown-menu-item,
    .linkflow-dropdown-menu-submenu-title {
      padding-top: 0;
      padding-bottom: 0;
      line-height: 28px;
    }

    .linkflow-dropdown-menu-submenu-active {
      > div {
        cursor: pointer;
        font-size: 14px;
        // background-color: #1264a3;
        background: rgba(107, 107, 108, 8%);
        border-radius: 6px;
        color: var(--primary-text-color-10);

        svg {
          fill: #fff;
        }
      }
    }

    .linkflow-dropdown-menu-item {
      line-height: 36px;
      cursor: pointer;
      font-size: 14px;
      padding: 0 18px;
    }
    .linkflow-dropdown-menu-item-active {
      background: rgba(107, 107, 108, 8%);
      border-radius: 6px;
      color: var(--primary-text-color-10);
    }
  }
}

.channelMenuContainer {
  width: 200px;
  background: #fff;
  box-shadow: 0 2px 4px 2px rgba(0, 0, 0, 4%);
  border-radius: 8px;
  padding: 6px;
  color: #1d1c1d;
  display: flex;
  flex-direction: column;

  .menuItemButton {
    display: flex;
    align-content: center;
    align-items: center;
    color: #1d1c1d;
    padding: 0 18px;
    cursor: pointer;
    line-height: 36px;
    height: 36px;
    font-size: 14px;
    font-weight: 400;
    &:hover {
      background: var(--msg-qute-backgroud-color);
      border-radius: 6px;
    }
    &.exit {
      color: #d6363f;
      &:hover {
        background: var(--msg-qute-backgroud-color);
        border-radius: 6px;
      }
    }
  }

  .menuSeparator {
    margin: 3px 0;
    border-top: 1px solid var(--primary-border-color);
  }
}

.botIcon {
  flex-shrink: 0;
  width: 41px;
  height: 16px;
  background: #fcf3e6;
  border-radius: 4px;
  border: 1px solid #cc8521;
  font-size: 11px;
  font-weight: 400;
  color: #cc8521;
  line-height: 16px;
  margin: 0 8px 0 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatarContainer {
  &.little {
    width: 32px;
    height: 32px;

    .avatarTotal1 {
      height: 15px;
      width: 15px;
      border-radius: 2px;
    }

    .avatarTotal2_1,
    .avatarTotal2_2,
    .avatarTotal3_1,
    .avatarTotal3_2,
    .avatarTotal3_3,
    .avatarTotal4_1,
    .avatarTotal4_2,
    .avatarTotal4_3,
    .avatarTotal4_4 {
      height: 15px;
      width: 15px;
    }

    .avatarTotal4_2 {
      height: 15px;
      width: 15px;
    }

    .avatarTotal5_1,
    .avatarTotal5_2,
    .avatarTotal5_3,
    .avatarTotal5_4,
    .avatarTotal5_5,
    .avatarTotal6_1,
    .avatarTotal6_2,
    .avatarTotal6_3,
    .avatarTotal6_4,
    .avatarTotal6_5,
    .avatarTotal6_6,
    .avatarTotal7_1,
    .avatarTotal7_2,
    .avatarTotal7_3,
    .avatarTotal7_4,
    .avatarTotal7_5,
    .avatarTotal7_6,
    .avatarTotal7_7,
    .avatarTotal8_1,
    .avatarTotal8_2,
    .avatarTotal8_3,
    .avatarTotal8_4,
    .avatarTotal8_5,
    .avatarTotal8_6,
    .avatarTotal8_7,
    .avatarTotal8_8,
    .avatarTotal9_1,
    .avatarTotal9_2,
    .avatarTotal9_3,
    .avatarTotal9_4,
    .avatarTotal9_5,
    .avatarTotal9_6,
    .avatarTotal9_7,
    .avatarTotal9_8,
    .avatarTotal9_9 {
      height: 10px;
      width: 10px;
    }
  }
  &.small {
    width: 38px;
    height: 38px;

    .avatarTotal1 {
      height: 18px;
      width: 18px;
      border-radius: 2px;
    }

    .avatarTotal2_1,
    .avatarTotal2_2,
    .avatarTotal3_1,
    .avatarTotal3_2,
    .avatarTotal3_3,
    .avatarTotal4_1,
    .avatarTotal4_2,
    .avatarTotal4_3,
    .avatarTotal4_4 {
      height: 18px;
      width: 18px;
    }

    .avatarTotal4_2 {
      height: 18px;
      width: 18px;
    }

    .avatarTotal5_1,
    .avatarTotal5_2,
    .avatarTotal5_3,
    .avatarTotal5_4,
    .avatarTotal5_5,
    .avatarTotal6_1,
    .avatarTotal6_2,
    .avatarTotal6_3,
    .avatarTotal6_4,
    .avatarTotal6_5,
    .avatarTotal6_6,
    .avatarTotal7_1,
    .avatarTotal7_2,
    .avatarTotal7_3,
    .avatarTotal7_4,
    .avatarTotal7_5,
    .avatarTotal7_6,
    .avatarTotal7_7,
    .avatarTotal8_1,
    .avatarTotal8_2,
    .avatarTotal8_3,
    .avatarTotal8_4,
    .avatarTotal8_5,
    .avatarTotal8_6,
    .avatarTotal8_7,
    .avatarTotal8_8,
    .avatarTotal9_1,
    .avatarTotal9_2,
    .avatarTotal9_3,
    .avatarTotal9_4,
    .avatarTotal9_5,
    .avatarTotal9_6,
    .avatarTotal9_7,
    .avatarTotal9_8,
    .avatarTotal9_9 {
      height: 12px;
      width: 12px;
    }
  }

  width: 40px;
  height: 40px;
  position: relative;
  background: rgba(210, 210, 210, 40%);
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    background-color: #d2d2d2;
  }
  .avatarTotal0 {
    height: 100%;
    width: 100%;
    border-radius: 2px;

    img {
      background-color: var(--primary-background-color-17);
    }
  }

  .avatarTotal1 {
    height: 20px;
    width: 20px;
    border-radius: 2px;

    img {
      background-color: var(--primary-background-color-17);
    }
  }

  .horizontalBox {
    align-items: center;
    display: flex;
    flex-direction: row;
    justify-content: center;
  }

  .verticalBox {
    align-items: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .avatarTotal2_1,
  .avatarTotal2_2,
  .avatarTotal3_1,
  .avatarTotal3_2,
  .avatarTotal3_3,
  .avatarTotal4_1,
  .avatarTotal4_2,
  .avatarTotal4_3,
  .avatarTotal4_4 {
    height: 20px;
    margin: 0 1px 1px 0;
    width: 20px;
  }

  .avatarTotal4_1 {
    object-fit: cover;
  }

  .avatarTotal4_2 {
    height: 20px;
    margin: 0 1px 1px 0;
    width: 20px;
  }

  .avatarTotal5_1,
  .avatarTotal5_2,
  .avatarTotal5_3,
  .avatarTotal5_4,
  .avatarTotal5_5,
  .avatarTotal6_1,
  .avatarTotal6_2,
  .avatarTotal6_3,
  .avatarTotal6_4,
  .avatarTotal6_5,
  .avatarTotal6_6,
  .avatarTotal7_1,
  .avatarTotal7_2,
  .avatarTotal7_3,
  .avatarTotal7_4,
  .avatarTotal7_5,
  .avatarTotal7_6,
  .avatarTotal7_7,
  .avatarTotal8_1,
  .avatarTotal8_2,
  .avatarTotal8_3,
  .avatarTotal8_4,
  .avatarTotal8_5,
  .avatarTotal8_6,
  .avatarTotal8_7,
  .avatarTotal8_8,
  .avatarTotal9_1,
  .avatarTotal9_2,
  .avatarTotal9_3,
  .avatarTotal9_4,
  .avatarTotal9_5,
  .avatarTotal9_6,
  .avatarTotal9_7,
  .avatarTotal9_8,
  .avatarTotal9_9 {
    height: 13px;
    margin: 0 1px 1px 0;
    width: 13px;
  }

  .avatarTotal9_9 {
    object-fit: cover;
  }
}
