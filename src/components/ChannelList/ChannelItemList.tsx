import classNames from 'classnames';
import {
  memo,
  ReactNode,
  useState,
  useEffect,
  useCallback,
  useMemo,
} from 'react';
import dayjs from 'dayjs';
import {
  CbEvents,
  ConversationItem,
  PublicUserItem,
  WSEvent,
} from '@ht/openim-wasm-client-sdk';
import { IMSDK } from '@/layouts/BasicLayout';
import { useUserStore } from '@/store';
import { UserStatusType } from '@/store/type';
import { Virtuoso } from '@ht/react-virtuoso';
import ChannelItem from './ChannelItem';
import styles from './index.less';

export interface UserDetailProps {
  status: UserStatusType;
  multiSession: number;
}
interface ChannleListItemProp {
  isActive: (prop: ConversationItem) => boolean;
  conversationIniting: boolean;
  allChatItems: ConversationItem[];
  // handleConversationClicked: (prop: any) => void;
  iconBoxRender?: (contact: ConversationItem) => ReactNode;
  extraItem?: (contact: ConversationItem) => ReactNode;
}
const ChannelItemList: React.FC<ChannleListItemProp> = ({
  isActive,
  conversationIniting,
  allChatItems,
  // handleConversationClicked,
  iconBoxRender = () => '',
  extraItem,
}) => {
  const [today, setToday] = useState(dayjs());
  const [userDetailMap, setUserDetailMap] =
    useState<Map<string, UserDetailProps>>();
  const { selfInfo, selfStatus } = useUserStore();
  useEffect(() => {
    let timer = null;
    timer = setInterval(() => {
      setToday(dayjs());
    }, 60000);
    return () => {
      if (timer) {
        clearInterval(timer);
      }
    };
  }, []);

  const getUserStatusFromDetail = useCallback(
    (userIDParam: string, userDetailParam: PublicUserItem) => {
      if (userIDParam === selfInfo.userID) {
        return selfStatus;
      } else {
        return JSON.parse(userDetailParam?.ex || '{}')?.userState;
      }
    },
    [selfInfo.userID, selfStatus]
  );

  const getMultiSessionFromDetail = useCallback(
    (userIDParam: string, userDetailParam: PublicUserItem) => {
      if (userIDParam === selfInfo.userID) {
        return JSON.parse(selfInfo?.ex || '{}')?.multiSession;
      } else {
        return JSON.parse(userDetailParam?.ex || '{}')?.multiSession;
      }
    },
    [selfInfo?.ex, selfInfo.userID]
  );

  // 私聊用户ID列表
  const userIDList = useMemo(() => {
    return allChatItems
      ?.filter((contact) => contact.userID !== '')
      ?.map((item) => item?.userID);
  }, [allChatItems]);

  // 批量查询用户详情信息
  useEffect(() => {
    const initUserDetailMap = async () => {
      const tempUserDetailMap = new Map();

      try {
        const userDetailDataResponse = await IMSDK.getUsersInfo([
          ...userIDList,
        ]);

        if (userDetailDataResponse?.data != null) {
          userDetailDataResponse.data.map((item) => {
            return tempUserDetailMap.set(item.userID, [
              {
                status: getUserStatusFromDetail(item.userID, item),
                multiSession: getMultiSessionFromDetail(item.userID, item),
              },
            ]);
          });

          setUserDetailMap(tempUserDetailMap);
        }
      } catch (e) {
        console.error('查询用户信息报错', e);
      }
    };

    initUserDetailMap();
  }, [getMultiSessionFromDetail, getUserStatusFromDetail, userIDList]);

  useEffect(() => {
    // 查询后回调触发
    const updateUserDetail = ({ data }: WSEvent<PublicUserItem>) => {
      if (userDetailMap != null) {
        const resUserDetailMap = new Map(userDetailMap);

        resUserDetailMap.set(data.userID, {
          status: getUserStatusFromDetail(data.userID, data),
          multiSession: getMultiSessionFromDetail(data.userID, data),
        });

        setUserDetailMap(resUserDetailMap);
      }
    };

    IMSDK.on(CbEvents.OnUserInfoUpdated, updateUserDetail);

    return () => {
      IMSDK.off(CbEvents.OnUserInfoUpdated, updateUserDetail);
    };
  }, [getMultiSessionFromDetail, getUserStatusFromDetail, userDetailMap]);

  return (
    <div
      className={classNames(styles.channelItemListWrapper)}
      id="conversationList"
    >
      {!conversationIniting && (
        <Virtuoso
          data={allChatItems}
          increaseViewportBy={{ top: 1200, bottom: 1200 }}
          fixedItemHeight={67} // 这里注意要跟ChannelItem组件的高度保持一致！
          itemContent={(index, contact) => {
            return (
              <ChannelItem
                key={contact.conversationID}
                contact={contact}
                iconBoxRender={iconBoxRender(contact)}
                isLastPinnedItem={
                  contact.isPinned && !allChatItems[index + 1]?.isPinned
                }
                isActive={isActive(contact)}
                userDetail={userDetailMap?.get(contact.userID)}
                today={today}
              >
                {extraItem?.(contact)}
              </ChannelItem>
            );
          }}
        />
      )}
    </div>
  );
};
export default memo(ChannelItemList);
