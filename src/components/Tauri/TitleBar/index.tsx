import { getCurrentWindow } from '@tauri-apps/api/window';
import closeIcon from '@/assets/closeIcon.svg';
import max from '@/assets/maximize.svg';
import min from '@/assets/minimize.svg';
import styles from './index.less';

const TitleBar = () => {
  return (
    <div data-tauri-drag-region={true} className={styles.titlebar}>
      <div
        onClick={() => {
          const curWin = getCurrentWindow();
          curWin.minimize();
        }}
        className={styles.titlebarbutton}
      >
        <img src={min} alt="minimize" />
      </div>
      <div
        onClick={() => {
          const curWin = getCurrentWindow();
          curWin.toggleMaximize();
        }}
        className={styles.titlebarbutton}
      >
        <img src={max} alt="maximize" />
      </div>
      <div
        onClick={() => {
          const curWin = getCurrentWindow();
          curWin.close();
        }}
        className={styles.titlebarbutton}
      >
        <img src={closeIcon} alt="close" />
      </div>
    </div>
  );
};

export default TitleBar;
