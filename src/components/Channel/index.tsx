import { memo, FC } from 'react';
import { useConversationStore } from '@/store';
import { isEmpty } from 'lodash';
import ChannelHeader from './components/ChannelHeader';
import styles from './index.less';
import ChannelArea from './components/ChannelArea';
import EmptyChannel from './components/EmptyChannel';
import ChannelRightArea from './components/ChannelRightArea';

export type ChannelHeaderTabType =
  | 'message'
  // | 'IM'
  // | 'files'
  // | 'bookmarks'
  // | 'pins'
  | 'docs';

export interface ChannelProps {
  hasDeleteIcon?: boolean;
  isSingleChat?: boolean;
}

const Channel: FC<ChannelProps> = (props) => {
  const { hasDeleteIcon = false, isSingleChat = false } = props;

  console.debug('重绘', Date.now());

  const currentConversation = useConversationStore(
    (state) => state.currentConversation
  );

  return (
    <div className={styles.channelWarp} data-id="channel-warp">
      {!isEmpty(currentConversation?.conversationID) ? (
        <div className={styles.channelContent}>
          <ChannelHeader hasDeleteIcon={hasDeleteIcon} />
          <ChannelArea />
        </div>
      ) : (
        <EmptyChannel />
      )}
      <ChannelRightArea hasDeleteIcon={hasDeleteIcon} />
    </div>
  );
};
export default memo(Channel);
