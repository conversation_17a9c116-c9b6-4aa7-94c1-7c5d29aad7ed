/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable indent */
/* eslint-disable max-lines */
import { FC, memo, useRef, forwardRef, useImperativeHandle } from 'react';
import { Form, Input, Select } from '@ht/sprite-ui';
import { FormInstance } from '@ht/sprite-ui/es/form/Form';
import { ConversationItem } from '@ht/openim-wasm-client-sdk';
import { IMSDK } from '@/layouts/BasicLayout';
import { BotConfigItem } from '@/store/type';
import { isEmpty } from 'lodash';
import classNames from 'classnames';
import OIMAvatar from '@/components/OIMAvatar';
import { getAvatarUrl } from '@/utils/avatar';
import { useConversationStore } from '@/store';
import { useSendMessage } from '@/hooks/useSendMessage';
import useUserInfo from '@/hooks/useUserInfo';
import dialogueSettingsIcon from '@/assets/channel/robotConversationList/dialogueSettings.svg';
import promptIcon from '@/assets/channel/robotConversationList/prompt.svg';
import { PopPersonCardContainer } from '../MessageItem/PopPersonCardContainer';
import styles from '../MessageItem/index.less';
import robotStyles from './index.less';

const { TextArea } = Input;
const { Option } = Select;

interface RobotConfigProps {
  userID: string;
  showName: string;
  botConfig: BotConfigItem[];
  botQuestions: string[];
  botIntro: string;
  currentConversation?: ConversationItem;
}

interface configFormRefType {
  configForm: FormInstance;
}

const RobotConfig: FC<RobotConfigProps> = ({
  userID,
  showName,
  botConfig,
  botQuestions,
  botIntro = '',
  currentConversation,
}) => {
  const configFormRef = useRef<configFormRefType>();

  const changeRightArea = useConversationStore(
    (state) => state.changeRightArea
  );
  const currentBotConfig = useConversationStore(
    (state) => state.currentBotConfig
  );
  const updateRobotConfig = useConversationStore(
    (state) => state.updateRobotConfig
  );
  const isConfig =
    !!(botConfig && botConfig?.length > 0) && isEmpty(currentBotConfig?.data);

  const isQuestion =
    !isConfig && ((botQuestions && botQuestions.length > 0) || botIntro);

  const submit = async () => {
    try {
      const values = await configFormRef.current?.configForm.validateFields();
      updateRobotConfig({
        data: values || {},
        config: botConfig,
      });
    } catch (err) {}
  };

  if (!isConfig && !isQuestion && !botIntro) {
    return <></>;
  }

  return (
    <div className={classNames(styles.userInfoRenderWarp, styles.isReceiver)}>
      <PopPersonCardContainer sendID={userID}>
        <div
          className={styles.avatarContainer}
          onClick={() => changeRightArea('OPEN_PERSON_DETAIL', userID)}
        >
          <OIMAvatar
            src={getAvatarUrl(userID)}
            userID={userID}
            hideOnlineStatus={true}
            size={40}
            shape="square"
          />
        </div>
      </PopPersonCardContainer>
      <div
        className={styles.detailContainer}
        style={
          isConfig
            ? { width: '560px', minWidth: '560px' }
            : { width: '452px', minWidth: '452px' }
        }
      >
        <div className={classNames(styles.senderInfo)}>
          <span className={styles.senderNickname}>{showName}</span>
        </div>
        <div
          className={classNames(
            styles.messageContentBox,
            styles.messageContentStyle
          )}
        >
          {isConfig ? (
            <div className={robotStyles.robotConfig}>
              <div className={robotStyles.headerBox}>
                <img src={dialogueSettingsIcon} />
                <span>{showName}-对话设置</span>
              </div>
              <div className={robotStyles.content}>
                <div className={robotStyles.formBox}>
                  <ConfigFormComponent
                    ref={configFormRef}
                    botConfig={botConfig}
                  />
                </div>
                <div className={robotStyles.submitBtn} onClick={() => submit()}>
                  开始会话
                </div>
              </div>
            </div>
          ) : (
            ''
          )}
          {isQuestion ? (
            <OpenQuestionComponent
              showName={showName}
              list={botQuestions}
              botIntro={botIntro}
              currentConversation={currentConversation}
            />
          ) : (
            ''
          )}
        </div>
      </div>
    </div>
  );
};
export default memo(RobotConfig);

interface ConfigFormComponentProps {
  botConfig: BotConfigItem[];
  defaultValue?: any;
}
const ConfigFormComponentRender = (
  { botConfig, defaultValue }: ConfigFormComponentProps,
  ref: any
) => {
  const [form] = Form.useForm();
  useImperativeHandle(
    ref,
    () => ({
      configForm: form,
    }),
    [form]
  );

  return (
    <Form form={form} layout="vertical" initialValues={{ ...defaultValue }}>
      {botConfig.map((item: BotConfigItem) => {
        if (item.type === 1) {
          return (
            <Form.Item
              key={item.key}
              label={
                <span
                  className={classNames(
                    robotStyles.formLabel,
                    item.required && robotStyles.itemRequired
                  )}
                >
                  {item.name || item.key}
                </span>
              }
              name={item.key}
              rules={
                item.required === 1
                  ? [
                      {
                        required: true,
                        message: `${item.name || item.key}是必填项`,
                      },
                    ]
                  : []
              }
              tooltip={{
                title: item.desc || item.name || item.key,
                icon: <img src={promptIcon} />,
              }}
            >
              <Input
                placeholder="请输入"
                size="large"
                maxLength={item.textMaxLength}
                autoComplete="off"
              />
            </Form.Item>
          );
        } else if (item.type === 2) {
          return (
            <Form.Item
              key={item.key}
              label={
                <span
                  className={classNames(
                    robotStyles.formLabel,
                    item.required && robotStyles.itemRequired
                  )}
                >
                  {item.name || item.key}
                </span>
              }
              name={item.key}
              rules={
                item.required === 1
                  ? [
                      {
                        required: true,
                        message: `${item.name || item.key}是必填项`,
                      },
                    ]
                  : []
              }
              tooltip={{
                title: item.desc || item.name || item.key,
                icon: <img src={promptIcon} />,
              }}
            >
              <TextArea rows={4} placeholder="请输入" />
            </Form.Item>
          );
        } else {
          return (
            <Form.Item
              key={item.key}
              label={
                <span
                  className={classNames(
                    robotStyles.formLabel,
                    item.required && robotStyles.itemRequired
                  )}
                >
                  {item.name || item.key}
                </span>
              }
              name={item.key}
              rules={
                item.required === 1
                  ? [
                      {
                        required: true,
                        message: `${item.name || item.key}是必选项`,
                      },
                    ]
                  : []
              }
              tooltip={{
                title: item.desc || item.name || item.key,
                icon: <img src={promptIcon} />,
              }}
            >
              <Select size="large" placeholder="请选择">
                {item.enumValues?.map((it) => {
                  return (
                    <Option key={it} value={it}>
                      {it}
                    </Option>
                  );
                })}
              </Select>
            </Form.Item>
          );
        }
      })}
    </Form>
  );
};

export const ConfigFormComponent = forwardRef(ConfigFormComponentRender);

const getOptionIcon = (num: number) => {
  const remainder = num % 8;
  switch (remainder) {
    case 0:
      return '🧠';
    case 1:
      return '💡';
    case 2:
      return '🔍';
    case 3:
      return '🤔';
    case 4:
      return '📄';
    case 5:
      return '🖊';
    case 6:
      return '📌';
    case 7:
      return '📍';
    default:
      return '🧠';
  }
};

interface OpenQuestionComponentProps {
  showName: string;
  list: string[];
  botIntro: string;
  currentConversation?: ConversationItem;
}

const OpenQuestionComponent: FC<OpenQuestionComponentProps> = ({
  showName,
  botIntro = '',
  list,
  currentConversation,
}) => {
  const currentMultiSession = useConversationStore(
    (state) => state.currentMultiSession
  );
  const { sendStreamMessage } = useSendMessage(currentMultiSession);
  const { userDetail } = useUserInfo(currentConversation?.userID);

  const btnClick = async (value: string) => {
    const message = (await IMSDK.createTextMessage(value)).data;
    sendStreamMessage({
      recvUser: userDetail,
      curMsg: message,
    });
  };

  return (
    <div className={robotStyles.openQuestionWarp}>
      <div className={robotStyles.title}>Hi！我是{showName}</div>
      <div className={robotStyles.desc}>
        {botIntro ? botIntro : '你可以试着问我：'}
      </div>
      <div className={robotStyles.optionsBox}>
        {list.map((item: string, index: number) => {
          return (
            // eslint-disable-next-line react/no-array-index-key
            <div className={robotStyles.item} key={`${index}-${item}`}>
              <div className={robotStyles.box} onClick={() => btnClick(item)}>
                <div className={robotStyles.icon}>{getOptionIcon(index)}</div>
                <div>{item}</div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
