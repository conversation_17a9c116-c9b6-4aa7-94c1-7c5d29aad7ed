import { FC, memo, useCallback, useEffect, useMemo, useState } from 'react';
import classNames from 'classnames';
import { isEmpty } from 'lodash';
import dayjs from 'dayjs';
import { ConversationItem, GroupMemberRole } from '@ht/openim-wasm-client-sdk';
import { getUserRoles, isBotUser, userRolesType } from '@/utils/avatar';
import OIMAvatar, { OnlineStatusComponent } from '@/components/OIMAvatar';
import {
  useConversationStore,
  useUserStore,
  useSearchInfoStore,
} from '@/store';
import BotIconComponent from '@/components/ChannelList/BotIconComponent';
import EditNameModal from '@/components/ConversationSetModal/components/EditNameModal';
import editIcon from '@/assets/channel/edit.png';
import { IMSDK } from '@/layouts/BasicLayout';
import { shallow } from 'zustand/shallow';
import { PopPersonCardContainer } from '../MessageItem/PopPersonCardContainer';
import RobotConfig from './RobotConfig';
import styles from './index.less';

interface MessageListForewordProps {
  isGroup: boolean;
  currentConversation?: ConversationItem;
  sourceFormEmpty?: boolean;
}
const MessageListForeword: FC<MessageListForewordProps> = ({
  isGroup,
  currentConversation,
  sourceFormEmpty = false,
}) => {
  const { userID = '', showName = '' } = currentConversation || {};
  const { selfInfo } = useUserStore.getState();
  const [groupCreaterInfo, setGroupCreaterInfo] = useState<{
    userID: string;
    userName: string;
  }>();

  const changeRightArea = useConversationStore(
    (state) => state.changeRightArea
  );
  const currentGroupInfo = useConversationStore(
    (state) => state.currentGroupInfo
  );
  const currentMemberInGroup = useConversationStore(
    (state) => state.currentMemberInGroup
  );
  const botConfig = useConversationStore((state) => state.botConfig);
  const botQuestions = useConversationStore((state) => state.botQuestions);
  const botIntro = useConversationStore((state) => state.botIntro);
  const isMultiSession = useConversationStore((state) => state.isMultiSession);
  const changeSearchRightArea = useSearchInfoStore(
    (state) => state.changeRightArea
  );
  const { createTime = '', introduction } = currentGroupInfo || {};
  const [modalVisible, setModalVisible] = useState<boolean>(false);

  const isRobot = useMemo(() => {
    return isBotUser(userID);
  }, [userID]);

  useEffect(() => {
    const getGroupCreater = async () => {
      // 群聊且有创建人id
      if (
        currentConversation?.groupID != null &&
        currentGroupInfo?.creatorUserID != null
      ) {
        if (currentGroupInfo?.creatorUserID !== selfInfo.userID) {
          // 创建人不是自己
          const { data: userInfo } = await IMSDK.getUsersInfo([
            currentGroupInfo?.creatorUserID,
          ]);

          setGroupCreaterInfo({
            userID: currentGroupInfo?.creatorUserID,
            userName: userInfo?.[0]?.nickname,
          });
        } else {
          // 创建者是自己
          setGroupCreaterInfo({
            userID: selfInfo.userID,
            userName: selfInfo.nickname,
          });
        }
      }
    };
    if (groupCreaterInfo != null) {
    } else {
      getGroupCreater();
    }
  }, [
    currentConversation,
    currentGroupInfo?.creatorUserID,
    groupCreaterInfo,
    selfInfo.nickname,
    selfInfo.userID,
  ]);

  const getCreateTime = useCallback((date: any) => {
    if (!date) {
      return;
    }
    const currentDate = dayjs(date);
    const today = dayjs();
    const yesterday = dayjs(today.date() - 1);

    if (currentDate.isSame(today)) {
      return '今天';
    } else if (currentDate.isSame(yesterday)) {
      return '昨天';
    } else {
      return `在 ${currentDate.format('M 月 D 日')}`;
    }
  }, []);

  const introductionEditRole = useMemo(() => {
    return (
      currentMemberInGroup?.groupID &&
      currentMemberInGroup.roleLevel !== GroupMemberRole.Normal
    );
  }, [currentMemberInGroup]);

  const hasValidInfo =
    groupCreaterInfo?.userID !== null && currentMemberInGroup?.userID !== null;

  const showCreaterInfo =
    hasValidInfo && groupCreaterInfo?.userID !== currentMemberInGroup?.userID;

  const showRobotConfig =
    sourceFormEmpty &&
    isMultiSession &&
    (botConfig?.length > 0 || botQuestions?.length > 0 || botIntro.length > 0);

  const handleChangeRightArea = () => {
    if (location.pathname === '/linkflow/search') {
      changeSearchRightArea('OPEN_PERSON_DETAIL', userID);
    } else {
      changeRightArea('OPEN_PERSON_DETAIL', userID);
    }
  };

  return (
    <div>
      <div
        className={classNames(
          styles.listForewordWrapper,
          isGroup && styles.listForewordGroupWrapper
        )}
      >
        {isGroup ? (
          <div className={styles.groupWrapper}>
            <div className={styles.showName}>{showName}</div>
            <div className={styles.descriptionWrapper}>
              {!hasValidInfo && '于'}
              {hasValidInfo &&
                groupCreaterInfo?.userID === currentMemberInGroup?.userID &&
                '你于'}
              {showCreaterInfo && (
                <>
                  <PopPersonCardContainer
                    sendID={groupCreaterInfo?.userID || ''}
                  >
                    <span
                      className={styles.highLightWrapper}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleChangeRightArea();
                      }}
                    >
                      @{groupCreaterInfo?.userName}
                    </span>
                  </PopPersonCardContainer>
                  &nbsp;于
                </>
              )}
              &nbsp;{getCreateTime(createTime)}&nbsp;创建此群聊。
              {/* {!isEmpty(introduction) && introductionEditRole && (
                <span>
                  {introduction}
                  {`(`}
                  <span
                    className={styles.highLightDesc}
                    onClick={() => setModalVisible(true)}
                  >
                    编辑说明
                  </span>
                  {`)`}
                </span>
              )} */}
            </div>
          </div>
        ) : (
          <div className={styles.singleWrapper}>
            <div className={styles.avatarsWrapper}>
              <OIMAvatar
                userID={userID}
                size={102}
                shape="square"
                style={{ marginRight: 12 }}
                borderRadius={12}
                hideOnlineStatus={true}
              />
              <span
                className={styles.showName}
                onClick={(e) => {
                  e.stopPropagation();
                  handleChangeRightArea();
                }}
              >
                {showName}
              </span>
              <BotIconComponent userID={userID} />
              <OnlineStatusComponent
                userID={userID}
                stateRight={0}
                stateSize={8}
                isPosition={false}
              />
            </div>
            {!isRobot && (
              <div className={styles.descriptionWrapper}>
                此对话仅限&nbsp;
                <PopPersonCardContainer sendID={userID}>
                  <span
                    className={styles.highLightWrapper}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleChangeRightArea();
                    }}
                  >
                    @{showName}
                  </span>
                </PopPersonCardContainer>
                &nbsp;和你之间，查看对方的个人档案进一步了解对方。
              </div>
            )}
            {!isRobot && (
              <div style={{ paddingBottom: '16px' }}>
                <div
                  className={styles.profileBtn}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleChangeRightArea();
                  }}
                >
                  查看个人档案
                </div>
              </div>
            )}
          </div>
        )}
        {modalVisible && (
          <EditNameModal
            visible={modalVisible}
            handleCancel={() => setModalVisible(false)}
            groupInfo={currentGroupInfo}
            modalType="introduction"
          />
        )}
      </div>
      {showRobotConfig ? (
        <RobotConfig
          userID={userID}
          showName={showName}
          botConfig={botConfig || []}
          botQuestions={botQuestions || []}
          botIntro={botIntro}
          currentConversation={currentConversation}
        />
      ) : (
        ''
      )}
    </div>
  );
};

export default memo(MessageListForeword);
