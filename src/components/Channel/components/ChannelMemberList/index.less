.channelMemberListWrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow-x: hidden;
  background: var(--primary-background-color-17);
  border-radius: 8px;
  margin-left: 10px;
  padding-top: 16px;
  .announcementWarp {
    max-height: 438px;
    padding: 0 20px 16px;
    cursor: pointer;
    .title {
      font-size: 18px;
      font-weight: 600;
      color: var(--primary-text-color-1);
      line-height: 28px;
      margin-bottom: 16px;
    }
    .announcementContent {
      max-height: 354px;
      overflow: hidden;
    }
  }

  .line {
    width: 100%;
    height: 1px;
    background: var(--primary-background-color-5);
    margin-bottom: 20px;
  }

  .headerTitle {
    padding: 0 20px;
    margin-bottom: 12px;
    font-size: 18px;
    font-weight: 600;
    color: #2f3035;
    line-height: 28px;
  }

  .memberList {
    flex: 1;
    padding-bottom: 10px;

    .memberItemWrapper {
      padding: 0 10px;

      .memberItem {
        padding: 8px 10px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        white-space: nowrap;

        .nickname {
          font-size: 14px;
          color: #666771;
          line-height: 22px;
          margin-left: 12px;
        }

        .tag {
          padding: 0 4px;
          height: 16px;
          border-radius: 4px;
          font-size: 11px;
          display: flex;
          align-items: center;
          margin-left: 12px;
          line-height: 16px;
        }

        .tag_1 {
          color: #e95400;
          background: #fff3ee;
          border: 1px solid #e95400;
        }

        .tag_2 {
          color: #0074e2;
          background: #eaf5ff;
          border: 1px solid #0074e2;
        }

        .tag_3 {
          color: #cc8521;
          background: #fcf3e6;
          border: 1px solid #cc8521;
        }

        &:hover {
          background: rgba(107, 107, 108, 8%);
          cursor: pointer;
        }
      }
    }
  }

  .virtuosoListContainer {
    &::-webkit-scrollbar {
      width: 8px;
      background-color: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background-color: transparent;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: transparent;
    }

    &:hover::-webkit-scrollbar {
      width: 8px;
    }

    &:hover::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 20%);
      border-radius: 3px;
    }

    &:hover::-webkit-scrollbar-track {
      background-color: transparent;
    }
  }
}
