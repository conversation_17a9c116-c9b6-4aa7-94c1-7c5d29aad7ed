.messageWarp {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;

  @inputBoxHeight: 164px;

  .messageUpWrap {
    position: absolute;
    padding-top: 4px;
    top: 0;
    width: calc(100% - 8px);
    left: 4px;
    z-index: 1;
    background-color: var(--primary-background-color-8);
  }

  .messageContent {
    flex: 1;
    // height: calc(100% - @inputBoxHeight);
    overflow-y: auto;
    .box {
      height: 100%;
      position: relative;
      // padding-left: 13px;
      .loadingBox {
        height: 100%;
        // display: flex;
        // justify-content: center;
        // align-items: center;
      }
      .virtuosoListContainer {
        height: 100%;
        overflow-y: auto;
        overflow-x: hidden;
        transform: translateZ(0);
        will-change: opacity;

        > div > div > div {
          &:last-of-type {
            .lastMultiSessionItem {
              height: 22px;
            }
          }
        }

        &::-webkit-scrollbar {
          width: 8px;
          background-color: transparent;
        }

        &::-webkit-scrollbar-thumb {
          background-color: transparent;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-track {
          background-color: transparent;
        }

        &:hover::-webkit-scrollbar {
          width: 8px;
        }

        &:hover::-webkit-scrollbar-thumb {
          background-color: rgba(0, 0, 0, 20%);
          border-radius: 3px;
        }

        &:hover::-webkit-scrollbar-track {
          background-color: transparent;
        }
      }
      .unReadCount {
        position: absolute;
        right: 38px;
        bottom: 12px;
        width: 110px;
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: var(--primary-background-color-6);
        box-shadow: 0 2px 14px 2px var(--primary-background-color-13);
        border-radius: 20px;
        font-size: 14px;
        font-weight: 500;
        color: var(--primary-text-color-9);
        cursor: pointer;
        > img {
          width: 14px;
          height: 14px;
          margin-left: 8px;
        }
      }

      .gotoFirstUnReadBtn {
        cursor: pointer;
        position: absolute;
        right: 20px;
        top: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 129px;
        height: 28px;
        background: #ffffff;
        box-shadow: 0 2px 14px 2px rgba(0, 0, 0, 10%);
        border-radius: 20px;

        span {
          font-size: 14px;
          font-weight: 600;
          color: #0074e2;
          line-height: 20px;
        }
        img {
          margin-left: 8px;
        }
      }
    }
  }

  .netErrorArea {
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    font-weight: 600;
    color: var(--primary-text-color-1);
    height: 100%;

    img {
      width: 28px;
      margin-right: 6px;
    }
  }

  .messageInputWrap {
    // min-height: @inputBoxHeight;
    padding: 24px 20px 20px;
    display: flex;
    flex-direction: column;
    position: relative;

    .typingStatus {
      flex-shrink: 0;
      font-size: 14px;
      font-weight: 400;
      color: var(--primary-text-color);
      line-height: 20px;
      height: 20px;
      position: absolute;
      top: 0;
      left: 22px;
    }
    @keyframes dot-sequence {
      0%,
      20% {
        content: ".";
      }
      40% {
        content: ". .";
      }
      60% {
        content: ". . .";
      }
      80%,
      100% {
        content: "";
      }
    }
    .dot {
      margin-left: 5px;
      &::after {
        width: 30px;
        content: ". . .";
        position: absolute;
        animation: dot-sequence 2s infinite;
      }
    }
  }
  .exitBox {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 66px;
    background: var(--primary-background-color-15);
    font-size: 16px;
    font-weight: 400;
    color: var(--primary-text-color-8);
    border-radius: 0 0 8px 8px;
    > img {
      width: 16px;
      margin-right: 6px;
    }
  }
}
