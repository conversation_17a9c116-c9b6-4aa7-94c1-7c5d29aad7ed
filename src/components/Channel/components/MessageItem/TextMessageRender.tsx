/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-statements */
/* eslint-disable react/no-danger */
/* eslint-disable indent */
import { FC, memo, useRef, useLayoutEffect, useState, useEffect } from 'react';
import classNames from 'classnames';
import Mark from 'mark.js';
import useUserInfo from '@/hooks/useUserInfo';
import _ from 'lodash';
import {
  MessageItem as MessageItemType,
  CbEvents,
} from '@ht/openim-wasm-client-sdk';
import refresh from '@/assets/contact/refresh.svg';
import { useTranslationStore } from '@/store/translationStore';
import { useConversationStore } from '@/store';
import { updateOneMessage } from '@/hooks/useHistoryMessageList';
import { getUserRoles, userRolesType } from '@/utils/avatar';
import { IMSDK } from '@/layouts/BasicLayout';
import { RenderMd } from '@/components/MdEditor/RenderMd';
import { shallow } from 'zustand/shallow';
import { AILoadingText } from '@/utils/constants';
import styles from './index.less';
import UserInfoRender from './UserInfoRender';
import RobotCommandRender from './RobotCommandRender';
import { startsWithSlashNoSpace } from '../MessageInput';
import TranslationRender from './TranslationRender';
import { IMessageItemProps } from '.';

interface TextMessageRenderProps extends IMessageItemProps {
  isSourceForQuote?: boolean;
}

const TextMessageRender: FC<TextMessageRenderProps> = ({ ...props }) => {
  const {
    message,
    isSender,
    scrollToBottomSmooth,
    hasScrolled,
    isSourceForQuote = false,
    isSearch = false,
    searchValue = '',
  } = props;
  const { currentConversation, llmLoading, updateLlmLoading } =
    useConversationStore(
      (state) => ({
        currentConversation: state.currentConversation,
        llmLoading: state.llmLoading,
        updateLlmLoading: state.updateLlmLoading,
      }),
      shallow
    );
  const userRoles = getUserRoles(message.sendID);
  const { userDetail } = useUserInfo(currentConversation?.userID);

  // 只获取当前消息的翻译数据，提高性能
  const translationData = useTranslationStore((state) =>
    message.clientMsgID ? state.translations[message.clientMsgID] : undefined
  );
  const [showRefresh, setShowRefresh] = useState(false);
  const [isStreaming, setIsStreaming] = useState<boolean>(false);
  const content =
    message.textElem?.content ||
    message.quoteElem?.text ||
    message.atTextElem?.text ||
    '';

  const markInstanceRef = useRef<any>(null);

  useEffect(() => {
    if (llmLoading && hasScrolled && isStreaming) {
      scrollToBottomSmooth?.();
    } else if (
      llmLoading &&
      isStreaming &&
      message.textElem?.content === AILoadingText
    ) {
      scrollToBottomSmooth?.();
    }
  }, [hasScrolled, showRefresh, llmLoading, message]);

  useEffect(() => {
    async function fetchStreamInfo() {
      if (!currentConversation?.latestMsg || !userDetail || isSender) {
        return;
      }
      const latestMsgObj = JSON.parse(currentConversation.latestMsg);
      const { seq } = latestMsgObj;
      if (userDetail?.ex) {
        try {
          const exInfo = JSON.parse(userDetail.ex);
          const { stream = false } = exInfo;
          // 只有当该消息的 seq 和最新消息的 seq 相等时才显示刷新按钮
          setShowRefresh(stream && seq === message.seq);
          setIsStreaming(stream && seq === message.seq);
        } catch (e) {
          console.warn('解析 ex 失败:', e);
        }
      }
    }
    fetchStreamInfo();
  }, [currentConversation?.latestMsg, message.seq, isSender, userDetail]);

  async function handleRefresh() {
    updateLlmLoading(true);
    let output = '';
    const newBotMessageReceived = ({ data }: any) => {
      if (data === 'DONE') {
        updateLlmLoading(false);
        IMSDK.off(CbEvents.OnSendMessageToBot, newBotMessageReceived);
        return;
      }
      output += data.content;
      if (data.type === 'seq' && Number(data.id) === message.seq) {
        updateOneMessage({
          clientMsgID: message.clientMsgID,
          textElem: {
            content: output,
          },
        } as MessageItemType);
      }
    };

    IMSDK.on(CbEvents.OnSendMessageToBot, newBotMessageReceived);
    await IMSDK.sendMsgToBot({
      senderID: message.recvID,
      recvID: message.sendID,
      data: message.content,
      senderNickname: message.senderNickname,
      receiverNickname: '',
      seq: message.seq,
    });
  }
  const [showExpand, setShowExpand] = useState(false);
  const [expanded, setExpanded] = useState(true);
  // const [mdHeight, setMdHeight] = useState(30);
  const containerRef = useRef<HTMLDivElement>(null);
  // translationData已经通过钩子直接获取

  // const getMdSize = (size: any) => {
  //   if (!size) {
  //     return;
  //   }
  //   const { height } = size;
  //   // setMdHeight(height);
  //   // setTooltipHeight(height);
  //   //
  //   if (Number(height) > 600) {
  //     setShowExpand(true);
  //   } else {
  //     setShowExpand(false);
  //   }
  // };

  // useUpdateLayoutEffect(() => {
  //   if (mdHeight > 0 && containerRef.current) {
  //     containerRef.current.style.height = `${mdHeight}px`;
  //   }
  // }, [mdHeight]);

  useEffect(() => {
    if (containerRef.current) {
      markInstanceRef.current = new Mark(containerRef.current);
    }
  }, []);

  useEffect(() => {
    if (markInstanceRef.current) {
      markInstanceRef.current.unmark();
      if (searchValue.trim() && isSearch) {
        markInstanceRef.current.mark(searchValue.trim(), {
          className: styles.search_Highlight,
        });
      }
    }
  }, [isSearch, searchValue]);

  const renderContent = () => {
    if (
      startsWithSlashNoSpace(content) &&
      (userRoles === userRolesType.developers ||
        userRoles === userRolesType.employees)
    ) {
      return <RobotCommandRender content={content} />;
    } else {
      return (
        <div>
          <div
            ref={containerRef}
            style={{
              // maxHeight: !expanded ? '600px' : '100%',
              // maxHeight:
              //   (isStreaming && llmLoading) || expanded ? '100%' : '600px',
              width: '100%',
              overflow: 'hidden',
              position: 'relative',
            }}
            className={classNames(
              styles.bubble,
              isSender ? styles.isSender : styles.isReceiver
            )}
          >
            <RenderMd
              // getSize={getMdSize}
              id={message.clientMsgID}
              value={content}
              isSender={isSender}
            />

            {/* {showExpand && !expanded && !(isStreaming && llmLoading) && (
              <div
                style={{
                  position: 'absolute',
                  bottom: 0,
                  height: '52px',
                  width: '100%',
                  background: `linear-gradient(transparent, ${
                    isSender
                      ? 'var(--primary-background-color-12)'
                      : 'var(--primary-background-color-6)'
                  })`,
                }}
              ></div>
            )} */}
          </div>
          {translationData && !isSearch && (
            <TranslationRender
              translationData={translationData}
              messageId={message.clientMsgID}
            />
          )}
          {showRefresh && !llmLoading && (
            <div style={{ paddingTop: '12px' }}>
              <div className={classNames(styles.refresh)}>
                <div
                  className={classNames(styles._refresh)}
                  onClick={() => handleRefresh()}
                >
                  <img
                    className={classNames(styles.refreshIcon)}
                    referrerPolicy="no-referrer"
                    src={refresh}
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      );
    }
  };

  return isSourceForQuote ? (
    renderContent()
  ) : (
    <UserInfoRender {...props}>{renderContent()}</UserInfoRender>
  );
};

export default memo(TextMessageRender);
