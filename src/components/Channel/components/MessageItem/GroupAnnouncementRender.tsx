import React, { FC, useRef, useEffect, useCallback } from 'react';
import Mark from 'mark.js';
import announcementIcon from '@/assets/channel/announcement.svg';
import { RenderMd } from '@/components/MdEditor/RenderMd';
import UserInfoRender from './UserInfoRender';
import { IMessageItemProps } from '.';
import styles from './index.less';

interface GroupAnnouncementRenderProps extends IMessageItemProps {
  isSourceForQuote?: boolean;
}

const GroupAnnouncementRender: FC<GroupAnnouncementRenderProps> = ({
  ...props
}) => {
  const {
    message,
    isSourceForQuote = false,
    isSearch = false,
    searchValue = '',
  } = props;
  const markInstanceRef = useRef<any>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (containerRef.current) {
      markInstanceRef.current = new Mark(containerRef.current);
    }
  }, []);

  useEffect(() => {
    if (markInstanceRef.current) {
      markInstanceRef.current.unmark();
      if (searchValue.trim() && isSearch) {
        markInstanceRef.current.mark(searchValue.trim(), {
          className: styles.search_Highlight,
        });
      }
    }
  }, [isSearch, searchValue]);

  const renderContent = useCallback(() => {
    const groupAnnouncementDetails = JSON.parse(
      message.notificationElem!.detail
    );
    const { group } = groupAnnouncementDetails || {};
    return (
      <div className={styles.groupAnnouncementRenderWarp} ref={containerRef}>
        {!isSearch && (
          <div className={styles.groupAnnouncementTitle}>
            <img src={announcementIcon} />
            <span>群公告</span>
          </div>
        )}
        <div
          className={styles.groupAnnouncementContent}
          style={isSearch ? { paddingTop: 0 } : {}}
        >
          <RenderMd
            id={message.clientMsgID}
            value={`${group?.notification}` || ''}
          />
        </div>
      </div>
    );
  }, [message]);

  return isSourceForQuote ? (
    renderContent()
  ) : (
    <UserInfoRender {...props}>{renderContent()}</UserInfoRender>
  );
};

export default GroupAnnouncementRender;
