/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-statements */
import React, { FC, useEffect, useRef, useState, memo } from 'react';
import { Spin, Tooltip, notification } from '@ht/sprite-ui';
import dayjs from 'dayjs';
import Mark from 'mark.js';
import { useDebounceFn } from 'ahooks';
import { IMSDK } from '@/layouts/BasicLayout';
import { useUserStore } from '@/store';
import { getDocIcon, docTypeEnum } from '@/utils/utils';
import linkIcon from '@/assets/channel/clouddocument/link.png';
import shareIcon from '@/assets/channel/clouddocument/share.png';
import deleteIcon from '@/assets/channel/clouddocument/delete.png';
import docDeleteIcon from '@/assets/channel/clouddocument/docDelete.png';
import hideIcon from '@/assets/images/messageItem/hideIcon.png';
import coverwordImage from '@/assets/channel/clouddocument/coverPage/wordCoverImage.png';
import coverexcelImage from '@/assets/channel/clouddocument/coverPage/excelCoverImage.png';
import coverpptImage from '@/assets/channel/clouddocument/coverPage/pptCoverImage.png';
import coverpdfImage from '@/assets/channel/clouddocument/coverPage/pdfCoverImage.png';
import coverMindImage from '@/assets/channel/clouddocument/coverPage/coverMindImage.png';
import smartdocsCover from '@/assets/channel/clouddocument/coverPage/smartdocsCover.png';
import coverDiagarmImage from '@/assets/channel/clouddocument/coverPage/coverDiagarmImage.png';
import coverMarkdown from '@/assets/channel/clouddocument/coverPage/markdownCover.png';
import coverApiTableImage from '@/assets/channel/clouddocument/coverPage/coverApiTableImage.png';
import ForwardModal from '@/components/ForwardModal';
import classNames from 'classnames';
import { IMessageItemProps } from '.';
import styles from './index.less';

/**
 * 获取默认封面图片
 * @param type 类型
 * @returns
 */
export const getDefaultCoverImg = (type: number) => {
  if (type === docTypeEnum.WORD) {
    return coverwordImage;
  } else if (type === docTypeEnum.EXCEL) {
    return coverexcelImage;
  } else if (type === docTypeEnum.PPT) {
    return coverpptImage;
  } else if (type === docTypeEnum.PDF) {
    return coverpdfImage;
  } else if (type === docTypeEnum.MINDMAP || type === docTypeEnum.MINDMAPNEW) {
    return coverMindImage;
  } else if (type === docTypeEnum.FLOWMAP) {
    return coverDiagarmImage;
  } else if (type === docTypeEnum.MARKDOWN) {
    return coverMarkdown;
  } else if (type === docTypeEnum.SmartDocs) {
    return smartdocsCover;
  } else if (type === docTypeEnum.APITABLE) {
    return coverApiTableImage;
  }
  return coverwordImage;
};

const DeleteComponent = (
  <div className={styles.clouddocumentContent}>
    <div className={styles.deleteHeader}>
      <div className={styles.deleteIcon}>
        <img src={deleteIcon} />
      </div>
      <div>文档已删除</div>
    </div>
    <div className={styles.deleteBox}>
      <img src={docDeleteIcon} />
      <div>文档已删除</div>
    </div>
  </div>
);

const ErrorComponent = (
  <div className={styles.clouddocumentContent}>
    <div className={styles.errorBox}>
      <img src={docDeleteIcon} />
      <div>加载失败</div>
    </div>
  </div>
);

const ClouddocumentRender: FC<IMessageItemProps> = ({ ...props }) => {
  const {
    message,
    isForwardMessage = false,
    isSender,
    isThread,
    historyTab = '',
    isSearch = false,
    searchValue = '',
  } = props;
  const selfID = useUserStore.getState().selfInfo.userID;
  const [loading, setLoading] = useState<boolean>(true);
  const [status, setStatus] = useState<'success' | 'error' | 'delete' | ''>('');
  const [docInfo, setDocIndo] = useState<any>({});
  const [forwardModal, setForwardModal] = useState<boolean>(false);

  const highLightContentRef = useRef(null);
  const markInstanceRef = useRef<any>(null);

  let content: any = {};
  try {
    const data = JSON.parse(message?.customElem?.data || '{}');
    content = data.content || {};
  } catch (e) {
    const data = message?.customElem?.data || {};
    content = data?.content || {};
  }

  const { run: runSearch } = useDebounceFn(
    () => {
      getDocInfo();
    },
    {
      wait: 300,
    }
  );

  useEffect(() => {
    runSearch();
  }, []);

  const getDocInfo = async () => {
    setLoading(true);
    try {
      const { data } = await IMSDK.getDocumentInfo({
        documentId: content.documentId,
        userId: selfID,
        isShortCut: content?.isShortCut || 2,
      });
      setDocIndo({
        ...content,
        ...data,
      });
      setStatus('success');
      setLoading(false);
    } catch (e) {
      console.error('getDocumentInfo', e);
      if (e?.errCode === 1004) {
        setStatus('delete');
      } else {
        setStatus('error');
      }
      setLoading(false);
    }
  };

  const applyForDoc = () => {
    if (docInfo.applyStatus === 0) {
      try {
        const params = {
          documentId: content.documentId,
          userId: selfID,
        };
        if (content?.isShortCut === 1) {
          params.documentId = docInfo?.originFileId || undefined;
        }
        IMSDK.applyForDocumentPermission(params);
        setDocIndo({
          ...docInfo,
          applyStatus: 1,
        });
        notification.open({
          message: `已向【${docInfo.createdBy}】申请文档访问权限`,
          duration: 3,
        });
      } catch (error) {
        console.warn('applyForDocumentPermission', error);
      }
    }
  };

  const preview = () => {
    const {
      documentId,
      fileCrdcId,
      documentType,
      isShortCut = 2,
      originFileId,
    } = docInfo || {};
    const url = `${origin}/htscPortalDocs/docs-for-preview-v2?fileCrdcId=${fileCrdcId}&documentId=${
      isShortCut === 1 ? originFileId : documentId
    }&documentType=${documentType}`;
    window.open(url, '_blank');
  };

  const renderForwardMessage = () => {
    return (
      <div className={styles.clouddocumentContent}>
        <div className={styles.docInfoBox}>
          <img
            src={getDocIcon(docInfo.documentType, docInfo?.isShortCut === 1)}
          />
          <div className={styles.docInfo}>
            <div className={styles.docName} title={docInfo.documentName}>
              {docInfo.documentName}
            </div>
            <div className={styles.docSource}>云文档</div>
          </div>
        </div>
      </div>
    );
  };

  useEffect(() => {
    if (
      highLightContentRef.current &&
      !markInstanceRef.current &&
      !loading &&
      status === 'success'
    ) {
      markInstanceRef.current = new Mark(highLightContentRef.current);
    }
  }, [loading, status]);

  useEffect(() => {
    if (markInstanceRef.current && !loading && status === 'success') {
      markInstanceRef.current.unmark();
      if (searchValue.trim() && isSearch) {
        markInstanceRef.current.mark(searchValue.trim(), {
          className: styles.search_Highlight,
        });
      }
    }
  }, [isSearch, searchValue, loading, status]);

  const messageDocRender = !isForwardMessage ? (
    <div className={styles.clouddocumentContent}>
      <div className={styles.docInfoBox} onClick={preview}>
        <img
          src={getDocIcon(docInfo.documentType, docInfo?.isShortCut === 1)}
        />
        <div className={styles.docInfo}>
          <div
            className={styles.docName}
            title={docInfo.documentName}
            ref={highLightContentRef}
          >
            {docInfo.documentName}
          </div>
          <div className={styles.docSource}>
            <span>创建者:</span>
            <span>{docInfo.createdBy}</span>
            <div className={styles.line}></div>
            <span>创建时间:</span>
            <span>{dayjs(docInfo.createdTime).format('YYYY-MM-DD HH:mm')}</span>
          </div>
        </div>
      </div>
      <div className={styles.imgBox} onClick={preview}>
        <img
          src={
            docInfo.coverPicUrl
              ? window.location.origin + docInfo.coverPicUrl
              : getDefaultCoverImg(docInfo.documentType)
          }
          style={docInfo.coverPicUrl ? {} : { marginTop: '-18px' }}
        />
      </div>
      <div
        className={styles.operateBox}
        style={{ cursor: 'pointer' }}
        onClick={() => setForwardModal(true)}
      >
        {/* <Tooltip
          placement="top"
          title="复制链接"
          overlayStyle={{ paddingBottom: '10px' }}
        >
          <div className={styles.operateItem}>
            <img src={linkIcon} />
          </div>
        </Tooltip> */}
        <Tooltip
          placement="top"
          title="转发"
          overlayStyle={{ paddingBottom: '10px' }}
        >
          <div
            className={styles.operateItem}
            onClick={() => setForwardModal(true)}
          >
            <img src={shareIcon} />
          </div>
        </Tooltip>
      </div>
      {!docInfo.roleCode && (
        <div className={styles.noRightsBox}>
          <div>您没有此文档的访问权限</div>
          <Tooltip
            placement="top"
            title="向文档所有者申请访问权限"
            overlayStyle={{ paddingBottom: '10px' }}
          >
            <div className={styles.applyForBtn} onClick={applyForDoc}>
              {docInfo.applyStatus === 0 ? '申请访问' : '申请中'}
            </div>
          </Tooltip>
        </div>
      )}
    </div>
  ) : (
    // 转发弹窗内展示
    renderForwardMessage()
  );

  return (
    <div className={styles.clouddocumentRender}>
      <div
        className={classNames(
          styles.clouddocumentRenderWarp,
          historyTab === 'docs' && styles.clouddocumentInHistoryDocsTab
        )}
        style={
          isForwardMessage
            ? { height: '64px', width: '100%' }
            : { width: '426px' }
        }
      >
        <Spin spinning={loading} wrapperClassName={styles.cloudSpinWarp}>
          {!loading && status === 'error' && ErrorComponent}
          {!loading && status === 'success' && messageDocRender}
          {!loading && status === 'delete' && DeleteComponent}
        </Spin>
      </div>
      {forwardModal && (
        <ForwardModal
          open={forwardModal}
          onClose={() => setForwardModal(false)}
          message={message}
          isSender={isSender}
          isThread={isThread}
        />
      )}
    </div>
  );
};

export default memo(ClouddocumentRender);
