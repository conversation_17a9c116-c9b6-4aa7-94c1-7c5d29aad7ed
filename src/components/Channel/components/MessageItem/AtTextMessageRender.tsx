/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable react/no-danger */
import React, { FC, memo } from 'react';
import { IMessageItemProps } from '.';
import UserInfoRender from './UserInfoRender';
import TextMessageRender from './TextMessageRender';
import QuoteMessageRender from './QuoteMessageRender';
import styles from './index.less';

const AtTextMessageRender: FC<IMessageItemProps> = ({ ...props }) => {
  const { message } = props || {};
  return (
    <UserInfoRender {...props}>
      <div className={styles.atTetxMessageRenderWarp}>
        <TextMessageRender {...props} isSourceForQuote={true} />
        {message.atTextElem?.quoteMessage ? (
          <div
            className={styles.atTetxQuoteContent}
            style={{ padding: '8px 0 0' }}
          >
            <QuoteMessageRender
              {...props}
              isForAt={true}
              message={message.atTextElem?.quoteMessage}
            />
          </div>
        ) : (
          ''
        )}
      </div>
    </UserInfoRender>
  );
};

export default memo(AtTextMessageRender);
