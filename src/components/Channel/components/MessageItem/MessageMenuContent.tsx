/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable indent */
import { memo, useRef, useCallback } from 'react';
import _ from 'lodash';
import { MessageItem, getSDK } from '@ht/openim-wasm-client-sdk';
import { Tooltip, message as Message } from '@ht/sprite-ui';
import { Emoji, EmojiStyle } from 'emoji-picker-react';
import plusOneIcon from '@/assets/channel/messageMenu/plusOne.png';
import emojiIcon from '@/assets/channel/messageMenu/emoji.png';
import messageIcon from '@/assets/channel/messageMenu/message.png';
import { useConversationStore, useUserStore } from '@/store';
import useThreadState from '@/hooks/useThreadState';
import EmojiPicker from '@/components/EmojiPicker';
import styles from './index.less';

type ReactionInfo = {
  emoji: string;
  userID: string;
};

const MessageMenuContent = ({
  message,
  closeMenu,
  isThread,
  conversationID,
}: {
  message: MessageItem;
  closeMenu?: () => void;
  isThread: boolean;
  conversationID: string;
}) => {
  const emojiPickerRef = useRef<any>(null);
  const { userID } = useUserStore.getState().selfInfo;

  const menuClick = (idx: number) => {
    const { handleClick } = messageMenuList?.[idx];
    if (handleClick != null) {
      handleClick();
    }
    closeMenu?.();
  };
  const currentConversation = useConversationStore(
    (state) => state.currentConversation
  );

  const { openThread } = useThreadState();

  const reactionClick = (emoji: string) => {
    const reactionList = message.attachedInfoElem?.reaction?.logs || [];
    const uniqList = _.uniqBy(reactionList, 'emoji');
    if (uniqList.length >= 36) {
      return Message.info('最多可添加36个表情');
    }
    const currentEmoji = _.filter(
      reactionList,
      (reactionItem: ReactionInfo) =>
        reactionItem.emoji === emoji && reactionItem.userID === userID
    );
    const sdk = getSDK();
    if (currentEmoji && currentEmoji.length > 0) {
      sdk.reactMessage({
        conversationID,
        clientMsgID: message.clientMsgID,
        reaction: { emoji, action: 2 },
      });
    } else {
      sdk.reactMessage({
        conversationID,
        clientMsgID: message.clientMsgID,
        reaction: { emoji, action: 1 },
      });
    }
  };

  const reactionChange = useCallback(
    _.throttle(reactionClick, 300, {
      leading: true,
      trailing: false,
    }),
    [message]
  );

  let messageMenuList = [
    {
      idx: 0,
      title: '点赞',
      icon: '',
      emojiIcon: (
        <span style={{ fontSize: '15px', cursor: 'pointer' }}>👍</span>
      ),
      width: 15,
      handleClick: () => {
        reactionChange('👍');
      },
    },
    {
      idx: 1,
      title: '+1',
      icon: plusOneIcon,
      width: 16,
      handleClick: () => {
        reactionChange('plusone');
      },
    },
    {
      idx: 2,
      title: '收到',
      icon: '',
      emojiIcon: (
        <span style={{ fontSize: '15px', cursor: 'pointer' }}>👌</span>
      ),
      width: 12,
      handleClick: () => {
        reactionChange('👌');
      },
    },
    {
      idx: 3,
      title: '查看更多表情',
      icon: emojiIcon,
      width: 16,
      handleClick: () => {},
    },
  ];
  // messageMenuList =
  //   !isThread && currentConversation?.groupID !== ''
  //     ? messageMenuList.concat({
  //         idx: 4,
  //         title: '在子群中回复',
  //         icon: messageIcon,
  //         width: 16,
  //         handleClick: () => openMessageColumn(),
  //       })
  //     : messageMenuList;

  const openMessageColumn = async () => {
    await openThread({
      parentId: currentConversation?.groupID || '',
      startClientMsg: message,
    });
  };

  return (
    <div className={styles.messageMenuContentWarp}>
      {messageMenuList.map((menu) => {
        return (
          <div
            key={menu.idx}
            onClick={() => menuClick(menu.idx)}
            onMouseDown={(e) => e.preventDefault()}
          >
            {menu.idx === 3 ? (
              <EmojiPicker
                ref={emojiPickerRef}
                message={message}
                conversationID={conversationID}
              >
                <Tooltip
                  title={menu.title ? menu.title : null}
                  overlayClassName={styles.tooltipWrap}
                >
                  <img width={menu.width} src={menu.icon} alt={menu.title} />
                </Tooltip>
              </EmojiPicker>
            ) : (
              <Tooltip
                title={menu.title ? menu.title : null}
                overlayClassName={styles.tooltipWrap}
              >
                {menu.emojiIcon ? (
                  menu.emojiIcon
                ) : (
                  <img width={menu.width} src={menu.icon} alt={menu.title} />
                )}
              </Tooltip>
            )}
          </div>
        );
      })}
    </div>
  );
};
export default memo(MessageMenuContent);
