import { FC, memo, useMemo } from 'react';
import classNames from 'classnames';
import _ from 'lodash';
import {
  MessageItem as MessageItemType,
  MessageStatus,
  MessageType,
} from '@ht/openim-wasm-client-sdk';
import { useTranslationStore } from '@/store/translationStore';
import { RenderMd } from '@/components/MdEditor';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import translateFinish from '@/assets/contact/translateFinish.png';
import MessageItem from '@/components/Channel/components/MessageItem';
import { IMessageItemProps } from '.';
import UserInfoRender from './UserInfoRender';
import styles from './index.less';

const MergeMessageRender: FC<IMessageItemProps> = ({ ...props }) => {
  const { message, isSender, disabled } = props;

  const { abstractList, multiMessage } = message.mergeElem || {};
  const { quoteMessage } = message.quoteElem || message.atTextElem || {};
  const { translations } = useTranslationStore();
  const translationData = translations[message.clientMsgID];

  const mergeMessageItemData = useMemo(() => {
    if (
      message.contentType === MessageType.MergeMessage &&
      _.includes(abstractList, 'createForwardMessage')
    ) {
      return multiMessage;
    } else if (
      message.contentType === MessageType.QuoteMessage ||
      message.contentType === MessageType.AtTextMessage
    ) {
      return !_.isEmpty(quoteMessage) ? [quoteMessage] : [];
    } else {
      return [];
    }
  }, [abstractList, message.contentType, multiMessage, quoteMessage]);

  const content =
    message.mergeElem?.title ||
    message.quoteElem?.text ||
    message.atTextElem?.text ||
    '';

  return (
    <div className={styles.mergeMessageWrapper}>
      <UserInfoRender {...props}>
        {!_.isEmpty(content) && (
          <div
            className={classNames(
              styles.bubble,
              isSender ? styles.isSender : styles.isReceiver
            )}
          >
            <RenderMd id={message.clientMsgID} value={content} />
          </div>
        )}
        {mergeMessageItemData ? (
          mergeMessageItemData.map((multiMessageItem: MessageItemType) => {
            if (multiMessageItem.contentType === MessageType.RevokeMessage) {
              return (
                <span
                  style={{ opacity: 0.6, fontSize: '15px' }}
                  key={multiMessageItem.clientMsgID}
                >
                  引用内容已撤回
                </span>
              );
            }
            return (
              <MessageItem
                key={multiMessageItem.clientMsgID}
                isThread={props.isThread}
                conversationID={props.conversationID}
                message={multiMessageItem}
                messageUpdateFlag={
                  multiMessageItem.senderNickname +
                  multiMessageItem.senderFaceUrl
                }
                isSender={isSender}
                showName={props.showName}
                isForwardMessage={true}
                isQuoteMessage={
                  message.contentType === MessageType.QuoteMessage
                }
                disabled={disabled}
              />
            );
          })
        ) : (
          <></>
        )}
        {translationData && (
          <div style={{ marginTop: '5px' }}>
            <div
              style={{ borderBottom: '1px solid #ccc', marginBottom: '5px' }}
            />
            {translationData.translationState === 'loading' && (
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <LoadingSpinner />
                <span
                  style={{
                    marginLeft: '5px',
                    // fontFamily: 'PingFangSC-Regular',
                    fontSize: '12px',
                  }}
                >
                  翻译中...
                </span>
              </div>
            )}
            {translationData.translationState === 'finish' && (
              <div style={{ color: '#333', lineHeight: '22px' }}>
                <span
                  style={{
                    fontSize: '14px',
                    color: 'rgba(29,28,29,1)',
                    // fontFamily: 'PingFangSC-Regular',
                  }}
                >
                  <RenderMd
                    // getSize={getMdSize}
                    id={message.clientMsgID}
                    value={translationData.translatedContent}
                  />
                  {/* {translationData.translatedContent} */}
                </span>
                <span
                  style={{
                    marginLeft: '5px',
                    whiteSpace: 'nowrap',
                    verticalAlign: 'middle',
                  }}
                >
                  <img
                    src={translateFinish}
                    alt="翻译完成"
                    style={{
                      marginRight: '5px',
                      width: '12px',
                      height: '12px',
                    }}
                  />
                  <span
                    style={{
                      // fontFamily: 'PingFangSC-Regular',
                      fontSize: '12px',
                      color: 'rgba(153,155,160,1)',
                    }}
                  >
                    翻译完成
                  </span>
                </span>
              </div>
            )}
          </div>
        )}
      </UserInfoRender>
    </div>
  );
};

export default memo(MergeMessageRender);
