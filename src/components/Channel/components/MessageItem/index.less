// .message-container-wrapper {
//   position: relative; // relative
//   display: flex; // flex
//   user-select: text; // select-text
//   padding: 0.75rem 1.25rem; // px-5 py-3
// }

// .message-container {
//   display: flex;
//   flex: 1;
//   overflow: hidden;

//   .message-wrap {
//     margin-left: 0.75rem; // ml-3
//     display: flex;
//     flex: 1;
//     flex-direction: column;
//     overflow: hidden;

//     .message-profile {
//       margin-bottom: 0.25rem; // mb-1
//       display: flex;
//       width: 100%;
//       font-size: 0.75rem; // text-xs
//     }

//     .message-nickname {
//       max-width: 30%; // max-w-[30%]
//       overflow: hidden; // truncate
//       text-overflow: ellipsis; // truncate
//       white-space: nowrap; // truncate
//       color: var(--sub-text); // text-[var(--sub-text)]

//       &-sender {
//         margin-left: 0.5rem; // ml-2
//       }

//       &-receiver {
//         margin-right: 0.5rem; // mr-2
//       }
//     }

//     .message-time {
//       color: var(--sub-text); // text-[var(--sub-text)]
//     }

//     .bubble {
//       width: fit-content; // w-fit
//       border-radius: 0.375rem; // rounded-md
//       padding: 0.625rem; // p-2.5
//       word-break: break-all;
//       background-color: var(--chat-bubble);
//     }

//     .suffix {
//       margin-left: 0.75rem; // ml-3
//       display: flex;
//       align-items: center;
//     }
//   }

//   .menu-wrap {
//     display: flex;
//     width: fit-content; // w-fit
//   }

//   &-sender {
//     flex-direction: row-reverse;

//     .message-wrap {
//       margin-right: 0.75rem; // mr-3
//       align-items: flex-end;

//       .message-profile {
//         flex-direction: row-reverse;
//       }

//       .bubble {
//         background-color: var(--chat-bubble-sender);
//       }

//       .suffix {
//         margin-left: 0;
//         margin-right: 0.75rem; // mr-3
//       }
//     }

//     .menu-wrap {
//       flex-direction: row-reverse;
//     }
//   }
// }

@keyframes message-highlight {
  20% {
    background: #f2c74433;
  }

  60% {
    background: #f2c74433;
  }
}

.bubble {
  width: 100%;
  // display: flex;
  font-size: 15px;
  font-weight: 400;
  color: var(--primary-text-color-4);
  line-height: 20px;
  word-break: break-all;
}

.groupCreatedRenderWarp {
  // display: flex;
  font-size: 15px;
  font-weight: 400;
  color: var(--primary-text-color-4);
  font-family: PingFangSC-Regular;
  word-break: break-all;
}

.isSender {
  flex-direction: row-reverse;

  .avatarContainer {
    margin-left: 12px;
  }

  .detailContainer {
    .senderInfo {
      flex-direction: row-reverse;

      .senderNickname {
        margin-right: 0;
        margin-left: 10px;
      }
    }
  }

  // .messageContentBox {
  //   float: right;
  // }
  .messageContentStyle {
    background: var(--primary-background-color-12);
    border-radius: 8px 0 8px 8px;

    .maskBox {
      border-radius: 8px 0 8px 8px;
    }
  }
}

.isReceiver {
  .avatarContainer {
    margin-right: 12px;
  }

  .messageContentStyle {
    background: var(--primary-background-color-6);
    border-radius: 0 8px 8px;

    .maskBox {
      border-radius: 0 8px 8px;
    }
  }
}

.userInfoRenderWarp {
  display: flex;
  padding: 8px 20px;

  &.userInfoRenderWarpMerged {
    padding: 0 24px;
    padding-left: 68px;

    .sendTime,
    .childMsgSendTime {
      display: inline-flex;
      width: 40px;
      height: 30px;
      line-height: 22px;
      align-items: flex-end;
      padding: 4px 0;
      // overflow: hidden;
      font-size: 12px;
      font-weight: 400;
      color: var(--primary-text-color-4);
      // line-height: 20px;
      visibility: hidden;
      position: relative;
      // top: 2px;
    }

    &:hover {
      .sendTime,
      .childMsgSendTime {
        visibility: visible;
      }
    }

    .childMsgSendTime {
      position: absolute;
      left: -40px;

      // top: 6px;
      &:hover {
        cursor: pointer;
        text-decoration: underline;
      }
    }
  }

  .childMsgSendTime {
    display: none;
  }

  .avatarContainer {
    padding-top: 3px;
    cursor: pointer;
    height: max-content;
  }

  .detailContainer {
    width: 52%;
    // min-width: 458px;
    // max-width: 1082px;
    //预览已读/未读/消息发送失败的位置 47px
    min-width: 505px;
    max-width: 1129px;
    // position: relative;
    // left: -12px;
    // overflow: hidden;
  }

  .senderInfo {
    display: flex;
    align-items: flex-end;
    padding-bottom: 3px;
    font-family: PingFangSC-Regular;

    .senderNameArea {
      height: 100%;
      display: flex;
      align-items: center;
    }

    .tag {
      padding: 0 4px;
      height: 16px;
      border-radius: 4px;
      font-size: 11px;
      display: flex;
      align-items: center;
      margin-left: -6px;
      line-height: 16px;
    }

    .tag_3 {
      color: #cc8521;
      background: #fcf3e6;
      border: 1px solid #cc8521;
      margin-right: 10px;
    }

    .senderNickname {
      margin-right: 10px;
      font-size: 14px;
      font-weight: 400;
      color: var(--primary-text-color-4);
      line-height: 18px;
      position: relative;
      // top: 1px;
      cursor: pointer;
    }

    .sendTime {
      font-size: 12px;
      font-weight: 400;
      color: var(--primary-text-color-4);
      line-height: 18px;
      // padding-bottom: 1px;
    }
  }

  .messageContentFileBox {
    max-width: 100%;
    width: max-content;
  }

  .messageContentBox {
    max-width: 100%;
    width: max-content;
    position: relative;

    .maskBox {
      // display: none;
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      background: var(--primary-background-color-13);
      z-index: 1;
    }
  }

  .messageContentStyle {
    padding: 12px 10px;
  }

  // .messageContentBox:hover {
  //   .maskBox {
  //     display: flex;
  //   }
  // }

  .view {
    width: 300px;
    padding: 12px 0;
    background: var(--primary-background-color-6);
    border-radius: 4px;
    border: 1px solid var(--primary-background-color-5);
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    // cursor: pointer;
    // width: 276px;
    // height: 72px;
    // border-radius: 4px;
    // border: 1px solid var(--primary-background-color-5);

    .threadHeader {
      padding: 0 16px 4px;
      display: flex;
      align-items: center;

      .title {
        color: var(--primary-text-color-1);
        margin-right: 12px;
        font-weight: 600;
        font-family: PingFangSC-Regular;
      }

      .number {
        color: var(--primary-color-3);
        cursor: pointer;
      }
    }

    .threadMeta {
      display: flex;
      align-items: center;
      padding: 0 16px;

      .message {
        flex: 1 1;
        font-size: 12px;
        padding: 0 12px;
        color: var(--primary-text-color-2);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .time {
        font-size: 12px;
        color: var(--offline-border-color);
      }
    }
  }

  .reactionWrapper {
    display: flex;
    align-items: center;
    padding-top: 8px;
    flex-wrap: wrap;
    padding-left: 1px;

    .reactionItem {
      width: fit-content;
      padding: 0 8px;
      height: 27px;
      background: var(--link-color-surf-pry);
      border-radius: 14px;
      color: var(--link-color-base-inv-pry);
      // margin: 0 4px 4px 0;
      justify-content: center;
      align-items: center;
      display: inline-flex;
      cursor: pointer;

      > span {
        min-width: 15px;
        text-align: center;
      }

      .reactionIcon {
        width: 16px;
        margin-right: 4px;
      }
      .reactionIcon1 {
        font-family: EmojiMart;
      }

      .reactionNum {
        font-size: 12px;
      }

      &:hover {
        box-shadow: 0 0 0 1px var(--link-color-content-ter);
      }
    }

    .reactionActivedItem {
      box-shadow: 0 0 0 1px var(--primary-text-color-9) !important;
      color: var(--primary-text-color-9) !important;
      background-color: rgba(0, 116, 226, 10%);
      font-weight: 600;
    }
  }
}

.userInfoRenderWarp_isSearch {
  padding: 0;

  .avatarContainer {
    margin-right: 12px;
  }

  .senderInfo {
    .senderNickname {
      font-size: 15px;
      font-weight: 600;
      color: var(--primary-text-color-10);
      line-height: 22px;
    }

    .sendTime {
      font-size: 12px;
      color: var(--primary-text-color-7);
      line-height: 22px;
      visibility: visible !important;
    }
  }

  .messageContentStyle {
    padding: 0;
    background-color: transparent;
  }

  .detailContainer {
    width: calc(100% - 52px);
    min-width: calc(100% - 52px);
    max-width: calc(100% - 52px);
  }
}

.messageRenderComponentWrapper {
  .userInfoRenderWarp_inHistoryList {
    background: rgba(107, 107, 108, 8%);
    border-radius: 8px;
  }
}
.userInfoRenderWarp_inHistoryList {
  padding: 15px 12px;
  margin: 0 8px;
  position: relative;
  cursor: default;

  .senderInfo {
    justify-content: space-between;
    white-space: nowrap;
    flex-wrap: nowrap;
  }

  .senderNickname {
    font-size: 14px !important;
    font-weight: 400 !important;
    color: var(--primary-text-color-7) !important;
    line-height: 22px !important;
  }

  .detailContainer {
    overflow: hidden;
  }

  // &:hover {
  //   background: rgba(107, 107, 108, 8%);
  //   border-radius: 8px;
  // }
}

.expandedBox {
  width: 100%;
  height: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;

  > div {
    display: flex;
    justify-content: center;
    align-items: center;
    // width: 70px;
    height: 28px;
    padding: 0 12px;
    // background: #FFFFFF;
    border-radius: 8px;
    border: 1px solid var(--primary-border-color-2);
    font-size: 14px;
    font-weight: 500;
    color: var(--primary-text-color-1);
    line-height: 20px;
    cursor: pointer;
    position: absolute;
    top: -2px;
    z-index: 1;

    > span {
      margin-right: 4px;
    }

    > img {
      width: 14px;
      height: 14px;
      // margin-left: 4px;
    }
  }
}

.userInfoRenderHighlight {
  animation: 3s forwards message-highlight;
}

.friendAdded {
  width: 100%;
  text-align: center;
  padding: 10px 0;
  font-size: 14px;
  font-weight: 400;
  color: var(--primary-text-color-4);
  line-height: 20px;

  .friendAddedTime {
    padding-bottom: 12px;
  }
}

.rightButtonMenu {
  width: 220px;
  border-radius: 8px;
  box-shadow: 0 2px 4px 2px rgba(0, 0, 0, 4%);
  overflow: hidden;

  :global {
    .linkflow-dropdown-menu {
      padding: 6px;
    }

    .linkflow-dropdown-menu-item {
      padding: 0;
      font-size: 14px;
      // font-weight: 600;
      font-weight: 500;
      color: var(--primary-text-color-1);
      border-radius: 6px;
    }

    .linkflow-dropdown-menu-item-divider {
      margin: 3px 0;
      background-color: var(--primary-background-color-5);
    }
  }
}

.rightButtonMenuItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 7px 18px;

  > div {
    display: flex;
    align-items: center;

    > img {
      margin-right: 8px;
    }
  }
}

.menuWrap {
  padding-left: 0;
  padding-right: 0;

  :global {
    .linkflow-popover-arrow {
      display: none;
    }

    .linkflow-popover-inner-content {
      padding: 0;
    }

    .linkflow-popover-content {
      padding-bottom: 0;
      padding-top: 0;
      // border-radius: 12px;
      // border: 1px solid var(--primary-background-color-5);
      // top: 25px;
    }

    .linkflow-popover-inner {
      // border-radius: 12px;
      background-color: transparent;
      box-shadow: none;
    }
  }
}

.tooltipWrap {
  padding-bottom: 10px;

  :global {
    .linkflow-tooltip-inner {
      padding: 6px 16px;
      font-size: 14px;
      font-weight: 400;
      color: var(--primary-background-color-6);
      line-height: 22px;
    }
  }
}

.messageMenuContentWarp {
  display: flex;
  align-items: center;
  border-radius: 8px;
  // margin: 0 8px;
  border: 1px solid var(--primary-background-color-5);
  padding: 4px 6px;
  background-color: var(--primary-background-color-6);
  font-family: EmojiMart;

  > div {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    border-radius: 6px;
    cursor: pointer;

    > div {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
    }

    img {
      cursor: pointer;
    }
  }

  > div:hover {
    background-color: var(--primary-text-color-5);
  }
}

.mseInfo {
  display: flex;
  justify-content: center;
  color: var(--primary-text-color-7);
  font-size: 13px;
  font-weight: 400;
  line-height: 20px;
  padding: 8px 20px;
  font-family: PingFangSC-Regular;
}

.mseInfo-channelItem {
  font-size: 13px;
  color: var(--primary-text-color-7);
  padding: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  > div {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.dateSeparator {
  height: 48px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 12px 0;
  font-size: 13px;
  font-weight: 400;
  color: var(--primary-text-color-7);
  line-height: 24px;

  :global {
    .linkflow-divider-inner-text {
      padding: 0;
    }

    .linkflow-divider-horizontal.linkflow-divider-with-text {
      border-top-color: var(--primary-border-color-1);
    }
  }

  .dateSeparatorButton {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 4px 33px;
    border: 1px solid var(--primary-background-color-5);
    border-radius: 20px;
    color: var(--primary-text-color-1);
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    font-family: PingFangSC-Regular;
  }
}

.historyListDateSeparator {
  height: auto;
  padding: 22px 20px 6px;
  font-size: 14px;
  color: var(--primary-text-color-1);
  line-height: 20px;
  justify-content: flex-start;
}

.historyListDateSeparator_first {
  height: auto;
  padding: 8px 20px 6px;
  font-size: 14px;
  color: var(--primary-text-color-1);
  line-height: 20px;
  justify-content: flex-start;
}

.dateDropdown {
  width: 235px;

  :global .linkflow-dropdown-menu {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: var(--primary-background-color-2);
  }

  :global .linkflow-dropdown-menu-item {
    width: 100%;
    padding: 5px 24px;

    &:hover {
      background-color: var(--primary-color);
      color: var(--primary-background-color-6);
    }
  }
}

.pictureWarp {
  .showPicture {
    max-width: 100%;
    cursor: pointer;
    object-fit: contain;
    border-radius: 8px;
  }

  .pictureMessageRenderContent {
    display: flex;
    width: 350px;
    height: 65px;
    padding: 0 12px;
    border-radius: 10px;
    border: 1px solid var(--offline-border-color);
    align-items: center;
    cursor: pointer;
    position: relative;
    background-color: var(--primary-background-color-6);

    .icon {
      flex-shrink: 0;
      width: 41px;
      height: 41px;
      border-radius: 8px;
      overflow: hidden;
      margin-right: 12px;
      display: flex;
      align-items: center;
      justify-content: center;

      > img {
        max-width: 40px;
        max-height: 40px;
      }
    }

    .fileInfo {
      flex: 1 1;
      display: flex;
      flex-direction: column;
      font-size: 12px;
      font-weight: 400;
      color: var(--primary-text-color);
      line-height: 17px;
      overflow: hidden;

      .fileName {
        width: 100%;
        font-size: 15px;
        font-weight: 600;
        font-family: PingFangSC-Regular;
        color: var(--primary-text-color-1);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-bottom: 2px;
      }

      .info {
        height: 17px;
      }
    }
  }
}

.fileMessageRenderWarp {
  .name {
    width: 350px;
    height: 30px;
    padding: 4px 0;
    display: flex;
    align-items: flex-end;
    color: var(--primary-text-color-4);
    font-size: 13px;

    > span {
      max-width: calc(100% - 22px);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .show {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 20px;
      height: 20px;
      margin-left: 10px;
      cursor: pointer;
      border-radius: 50%;

      > img {
        width: 14px;
      }
    }

    .show:hover {
      background-color: var(--primary-text-color-5);
    }

    .hide {
      > img {
        transform: rotate(-90deg);
      }
    }
  }

  .fileMessageRenderContent {
    display: flex;
    width: 350px;
    height: 65px;
    padding: 0 12px;
    border-radius: 10px;
    border: 1px solid var(--offline-border-color);
    align-items: center;
    cursor: pointer;
    position: relative;
    background-color: var(--primary-background-color-6);

    .icon {
      flex-shrink: 0;
      width: 41px;
      height: 41px;
      border-radius: 8px;
      overflow: hidden;
      margin-right: 12px;
      display: flex;
      align-items: center;
      justify-content: center;

      > img {
        width: 40px;
        height: 40px;
      }
    }

    .fileInfo {
      flex: 1 1;
      display: flex;
      flex-direction: column;
      font-size: 12px;
      font-weight: 400;
      color: var(--primary-text-color);
      line-height: 17px;
      overflow: hidden;

      .fileName {
        width: 100%;
        font-size: 15px;
        font-weight: 600;
        font-family: PingFangSC-Regular;
        color: var(--primary-text-color-1);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding-bottom: 2px;
      }

      .info {
        height: 17px;
      }
    }

    .operationMenu {
      display: none;
      position: absolute;
      right: 15px;
      margin: auto 0;
      padding: 4px;
      box-shadow: 0 2px 4px 0 var(--file-backgroud-color);
      border-radius: 12px;
      border: 1px solid var(--primary-background-color-5);
      background-color: var(--primary-background-color-6);

      .operationItem {
        width: 32px;
        height: 32px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
      }

      .operationItem:hover {
        background: var(--file-backgroud-color);
      }
    }

    .operationMoreMenuWarp {
      width: 220px;
      padding-right: 0;
      border-radius: 8px;
      border: 1px solid var(--primary-background-color-5);
      overflow: hidden;
      background-color: var(--primary-background-color-6);
      padding-left: 0;
      z-index: 1031;

      :global {
        .linkflow-popover-arrow {
          display: none;
        }

        .linkflow-popover-inner-content {
          padding: 0;
        }
      }
    }

    .moreMenuWarp {
      .menuBox {
        padding: 12px 0;

        .menuItem {
          width: 100%;
          height: 30px;
          display: flex;
          align-items: center;
          padding: 0 24px;
          font-size: 14px;
          font-weight: 400;
          color: var(--primary-text-color);
          cursor: pointer;
        }

        .menuItem:hover {
          // color: var(--primary-text-color-pressed);
          background-color: var(--msg-qute-backgroud-color);
        }

        .deleteBtn {
          color: var(--link-color-content-imp);
        }

        .deleteBtn:hover {
          // color: var(--primary-text-color-pressed);
          background-color: var(--msg-qute-backgroud-color);
        }
      }

      .menuBox:first-child {
        border-bottom: 1px solid var(--primary-background-color-5);
      }

      .menuBox:last-child {
        border-bottom: none;
      }
    }
  }

  .fileMessageRenderContent:hover {
    .operationMenu {
      display: flex;
    }
  }
}

.popPersonCardContainer {
  :global {
    .linkflow-popover-inner-content {
      padding: 0;
    }

    .linkflow-popover-arrow {
      display: none;
    }

    .linkflow-popover-content {
      border-radius: 10px;
    }
  }
}

.clouddocumentRender {
  .name {
    height: 30px;
    padding: 4px 0;
    display: flex;
    align-items: flex-end;
    color: var(--primary-text-color-4);
    font-size: 13px;

    .show {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 20px;
      height: 20px;
      margin-left: 10px;
      cursor: pointer;
      border-radius: 50%;

      > img {
        width: 14px;
      }
    }

    .show:hover {
      background-color: var(--primary-text-color-5);
    }

    .hide {
      > img {
        transform: rotate(-90deg);
      }
    }
  }
}

.clouddocumentRenderWarp {
  width: 426px;
  height: 268px;
  border-radius: 8px;
  border: 1px solid var(--primary-border-color);
  overflow: hidden;

  .clouddocumentContent {
    width: 100%;
    height: 100%;
    position: relative;

    .docInfoBox {
      display: flex;
      align-items: center;
      height: 64px;
      padding: 0 12px;
      background: var(--primary-background-color-6);
      border-bottom: 1px solid var(--primary-background-color-5);
      cursor: pointer;

      > img {
        width: 40px;
        height: 40px;
        margin-right: 13px;
      }

      .docInfo {
        width: calc(100% - 53px);

        .docName {
          font-size: 16px;
          font-weight: 600;
          color: var(--primary-text-color-1);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .docSource {
          display: flex;
          align-items: center;
          font-size: 12px;
          font-weight: 400;
          color: var(--primary-text-color);

          .line {
            width: 1px;
            height: 14px;
            background: var(--primary-background-color-5);
            margin: 0 4px;
          }
        }
      }
    }

    .imgBox {
      width: 100%;
      height: 201px;
      position: relative;
      overflow: hidden;
      cursor: pointer;
      background-color: var(--primary-background-color-6);

      > img {
        width: calc(100% + 4px);
        margin-left: -2px;
        object-fit: contain;
      }
    }

    .operateBox {
      // width: 72px;
      width: 47px;
      height: 40px;
      display: none;
      align-items: center;
      justify-content: center;
      background: var(--primary-background-color-6);
      box-shadow: 0 2px 4px 0 var(--file-backgroud-color);
      border-radius: 12px;
      border: 1px solid var(--primary-border-color);
      position: absolute;
      right: 12px;
      top: 12px;

      > div {
        width: 25px;
        height: 25px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;

        > img {
          width: 20px;
          height: 20px;
        }
      }

      // > div:hover {
      //   background-color: var(--file-backgroud-color);
      // }
    }

    .noRightsBox {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 40px;
      padding: 0 15px;
      font-size: 14px;
      font-weight: 400;
      color: var(--primary-text-color-3);
      background: var(--primary-background-color-6);
      position: absolute;
      left: 0;
      bottom: 0;

      .applyForBtn {
        color: var(--primary-background-color-4);
        font-weight: 600;
        cursor: pointer;
      }
    }

    .deleteHeader {
      display: flex;
      align-items: center;
      height: 64px;
      padding: 0 12px;
      background: var(--primary-background-color-6);
      font-size: 16px;
      font-weight: 400;
      color: var(--primary-text-color);
      border-bottom: 1px solid var(--primary-background-color-5);

      .deleteIcon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        background-color: var(--primary-background-color-5);
        border-radius: 8px;
        margin-right: 13px;

        > img {
          width: 20px;
          height: 20px;
        }
      }
    }

    .deleteBox {
      width: 100%;
      height: 201px;
      text-align: center;
      font-weight: 400;
      font-size: 14px;
      color: var(--primary-text-color);

      > img {
        margin: 29px 0 8px;
      }
    }

    .errorBox {
      width: 100%;
      height: 100%;
      text-align: center;
      font-weight: 400;
      font-size: 14px;
      color: var(--primary-text-color);

      > img {
        margin: 64px 0 8px;
      }
    }
  }

  .clouddocumentContent:hover {
    .operateBox {
      display: flex;
    }
  }

  .cloudDocRrror {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 265px;
    font-size: 16px;
    font-weight: 400;
    color: var(--primary-text-color-3);
  }
}

.clouddocumentInHistoryDocsTab {
  height: 64px;
  max-width: 100%;
  min-width: 350px;

  .imgBox {
    display: none;
  }

  .noRightsBox {
    display: none !important;
  }

  .docInfoBox {
    border-bottom: none !important;
  }
}

.cloudSpinWarp {
  height: 100%;
}

.refresh {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  margin-left: 0;
  // margin-top: 12px;
  justify-content: center;
  align-items: center;
  display: flex;
  z-index: 1;

  &:hover {
    background: var(--primary-background-color-14);
  }
}

._refresh {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.refreshIcon {
  width: 16px;
  height: 16px;
}

.refreshInfo {
  font-size: 12px;
  color: #333;
}

.quoteMessageRenderWarp {
  // max-width: 426px;
  // width: max-content;
  background: var(--msg-qute-backgroud-color);
  border-radius: 8px;
  padding: 3px 16px;
  font-size: 12px;
  font-weight: 500;
  color: var(--primary-text-color);
  position: relative;
  z-index: 1;

  // overflow: hidden;
  // text-overflow: ellipsis;
  // white-space: nowrap;
  .quoteContent {
    width: 100%;
    display: flex;

    .senderNickname {
      flex-shrink: 0;
      padding-top: 5px;
      line-height: 21px;
      font-size: 13px;
    }

    .box {
      flex: 1 1;

      .textBox {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 13px;
        font-weight: 400;
        color: var(--primary-text-color-7);
        line-height: 22px;
        // padding: 5px 0;
        word-break: break-all;
        font-family: lato, EmojiMart;
      }
    }

    .imgWarp {
      padding: 5px 0;
      > img {
        width: 36px;
        height: 36px;
        border-radius: 7px;
        object-fit: cover;
      }
    }

    .fileWarp {
      display: flex;
      padding: 5px 0 8px;
      line-height: 21px;

      > div {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      > img {
        flex-shrink: 0;
        width: 18px;
        height: 18px;
        margin-right: 6px;
        margin-top: 2px;
      }
    }

    .revokeWarp {
      line-height: 22px;
      padding: 5px 0;
    }
  }
}

.quoteMessageRenderWarpDisabled {
  display: none;
}

.atTetxMessageRenderWarp {
  .atTetxQuoteContent {
    display: flex;
  }
}

.robotCommandRenderWarp {
  display: flex;
  align-items: center;

  .robotCommandBox {
    display: flex;
    height: 24px;
    background: rgba(0, 116, 226, 10%);
    border-radius: 4px;
    border: 1px solid var(--primary-text-color-9);
    margin-left: 8px;

    .addonBefore {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 21px;
      background: var(--primary-text-color-9);
      border-radius: 3px 0 0 3px;
      color: #fff;
      margin: 1px;
    }

    .command {
      display: flex;
      align-items: center;
      font-size: 15px;
      color: var(--primary-text-color-pressed);
      background: var(--primary-text-color-9);
      border-radius: 0 3px 3px 0;
      padding: 0 8px;
      margin: 1px;
      line-height: 22px;
    }
  }
}

.robotCommandPopoverWarp {
  padding-bottom: 4px;
  padding-top: 4px;

  :global {
    .linkflow-popover-arrow {
      display: none;
    }

    .linkflow-popover-inner-content {
      padding: 0;
    }

    .linkflow-popover-content {
      padding-bottom: 0;
      padding-top: 0;
    }

    .linkflow-popover-inner {
      background-color: transparent;
      box-shadow: none;
    }
  }
}

.commandInfo {
  max-width: 500px;
  padding: 12px 16px;
  background: var(--primary-background-color-6);
  box-shadow: 0 2px 4px 2px rgba(0, 0, 0, 4%);
  border-radius: 8px;

  .title {
    font-size: 12px;
    font-weight: 400;
    color: var(--primary-text-color-7);
    line-height: 16px;
    margin-bottom: 12px;
  }

  .item {
    display: flex;
    // align-items: center;
    font-size: 14px;
    font-weight: 400;
    color: var(--primary-text-color-1);
    line-height: 20px;
    .lable {
      display: flex;
      align-items: center;
      height: 20px;
    }

    .dot {
      flex-shrink: 0;
      width: 4px;
      height: 4px;
      margin-right: 6px;
      border-radius: 50%;
      background: var(--primary-text-color-1);
    }
  }
}

.search_Highlight {
  padding: 0 !important;
  background: #fff5da !important;
  border-radius: 2px;
}
.separator {
  display: flex;
  align-items: center;
  padding: 20px 20px 27px;

  .lineLeft {
    flex-grow: 1;
    height: 1px;
    background-color: #ccc;
    margin-right: 32px;
  }

  .text {
    font-size: 13px;
    font-weight: 400;
    color: #666771;
    line-height: 24px;
    white-space: nowrap;
  }
  .lineRight {
    flex-grow: 1;
    height: 1px;
    background-color: #ccc;
    margin-left: 32px;
  }
}

.confirmText {
  font-size: 15px;
  font-weight: 400;
  color: #1d1c1d;
  line-height: 22px;
}
