/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable indent */
/* eslint-disable max-lines */
/* eslint-disable max-statements */
import React, { FC, memo, useMemo, useState } from 'react';
import {
  CbEvents,
  MessageItem as MessageItemType,
  MessageType,
  MessageStatus,
  GroupStatus,
  GroupMemberRole,
} from '@ht/openim-wasm-client-sdk';
import copyIcon from '@/assets/channel/rightButtonMenu/copy.svg';
import emojiIcon from '@/assets/channel/rightButtonMenu/emoji.svg';
import forwardIcon from '@/assets/channel/rightButtonMenu/forward.svg';
import msgUpIcon from '@/assets/channel/rightButtonMenu/topMsg.svg';
import msgUpClearIcon from '@/assets/channel/rightButtonMenu/topMsgClear.svg';
import translateIcon from '@/assets/channel/rightButtonMenu/translate.svg';
import translateCancelIcon from '@/assets/channel/rightButtonMenu/translateCancel.svg';
import quoteIcon from '@/assets/channel/rightButtonMenu/quote.svg';
import type { MenuProps } from '@ht/sprite-ui';
import { Menu } from '@ht/sprite-ui';
import { feedbackToast } from '@/utils/common';
import { IMSDK } from '@/layouts/BasicLayout';
import { emit } from '@/utils/events';
import { deleteOneMessage } from '@/hooks/useHistoryMessageList';
import { useConversationStore } from '@/store';
import useThreadState from '@/hooks/useThreadState';
import { useTranslationStore } from '@/store/translationStore';
import EmojiPicker from '@/components/EmojiPicker';
import { useConversationSettings } from '@/hooks/useConversationSettings';
import { getMessageContent, handleCopy } from '@/utils/message';
import { selectedTextRef } from '@/hooks/useSelectedText';
import { getUserRoles, userRolesType } from '@/utils/avatar';
import { shallow } from 'zustand/shallow';
import ConfirmModal from '@/components/ConfirmModal';
import closeIcon from '@/assets/images/chatSetting/closeIcon.svg';
import styles from './index.less';

export function isWithin24Hours(targetDate: number) {
  // 获取当前时间
  const now = new Date().getTime();
  // 计算时间差（毫秒）
  const diffInMs = now - targetDate;
  // 24小时的毫秒数
  const twentyFourHoursInMs = 24 * 60 * 60 * 1000;

  // 判断时间差是否小于24小时且目标时间不晚于当前时间
  return diffInMs >= 0 && diffInMs <= twentyFourHoursInMs;
}

const menus = [
  'copy',
  'emoji',
  'thread',
  'reply',
  'forwardMsg',
  'msgUp',
  'msgUpClear',
  'divider',
  'translateMsg',
  'clearTranslateMsg',
  // 'check',
  // 'collect',
  // 'divider',
  // 'copyLink',
  'divider',
  'remove',
];
const myMenus = [
  'copy',
  'emoji',
  'thread',
  'reply',
  'forwardMsg',
  'msgUp',
  'msgUpClear',
  'divider',
  'translateMsg',
  'clearTranslateMsg',
  // 'check',
  // 'collect',
  // 'divider',
  // 'copyLink',
  'divider',
  'revoke',
];
const robotMenus = [
  'emoji',
  'thread',
  'reply',
  'forwardMsg',
  'msgUp',
  'msgUpClear',
  // 'check',
  // 'collect',
  // 'divider',
  // 'copyLink',
  'divider',
  'remove',
];
const threadMenus = [
  'emoji',
  // 'forward',
  // 'view',
  // 'divider',
  // 'check',
  // 'collect',
  // 'divider',
  // 'copyLink',
  'divider',
  'remove',
];

const multiSessionMenus = [
  'copy',
  'forwardMsg',
  'translateMsg',
  'clearTranslateMsg',
  'remove',
];
const groupAnnouncement = [
  'emoji',
  'copy',
  'reply',
  'forwardMsg',
  'msgUp',
  'msgUpClear',
  'divider',
  'remove',
];

interface RightButtonMenuProps {
  message: MessageItemType;
  isSender: boolean;
  isThread: boolean;
  showTreadBtnVisible: boolean;
  conversationID: string;
  openForwardModal: () => void;
  setRightBtnOpen: (val: boolean) => void;
  isExit: boolean;
}

const RightButtonMenu: FC<RightButtonMenuProps> = ({
  message,
  isSender,
  isThread,
  showTreadBtnVisible,
  conversationID,
  openForwardModal,
  setRightBtnOpen,
  isExit,
}) => {
  // const selfUserID = useUserStore((state) => state.selfInfo.userID);
  const currentConversation = useConversationStore(
    (state) => state.currentConversation
  );
  const currentGroupInfo = useConversationStore(
    (state) => state.currentGroupInfo
  );
  const currentMemberInGroup = useConversationStore(
    (state) => state.currentMemberInGroup
  );
  const updateTranslationState = useTranslationStore(
    (state) => state.updateTranslationState
  );

  const currentMessageUpInfo = useConversationStore(
    (state) => state.currentMessageUpInfo
  );
  const isMultiSession = useConversationStore((state) => state.isMultiSession);
  const getCurrentMessageUpInfo = useConversationStore(
    (state) => state.getCurrentMessageUpInfo
  );

  const updateTranslatedContent = useTranslationStore(
    (state) => state.updateTranslatedContent
  );
  const translations = useTranslationStore((state) => state.translations);
  const clearTranslation = useTranslationStore(
    (state) => state.clearTranslation
  );
  const { openThread } = useThreadState();

  const { markConversationMessageAsRead } =
    useConversationSettings(currentConversation);

  const [confirmModalVisible, setConfirmModalVisible] =
    useState<boolean>(false);
  const [clearUpConfirmModalVisible, setClearUpConfirmModalVisible] =
    useState<boolean>(false);

  const translationData = translations[message.clientMsgID];
  const menuButtons: menuButtonType = useMemo(() => {
    const tempMenu = {
      copy: {
        label: (
          <div className={styles.rightButtonMenuItem}>
            <div>
              <img src={copyIcon} />
              <span>复制</span>
            </div>
          </div>
        ),
        key: 'copy',
      },
      emoji: {
        label: (
          <EmojiPicker message={message} conversationID={conversationID}>
            <div className={styles.rightButtonMenuItem}>
              <div>
                <img src={emojiIcon} />
                <span>表情回复</span>
              </div>
              {/* <span>R</span> */}
            </div>
          </EmojiPicker>
        ),
        key: 'emoji',
      },
      reply: {
        label: (
          <div className={styles.rightButtonMenuItem}>
            <div>
              <img src={quoteIcon} />
              <span>引用</span>
            </div>
          </div>
        ),
        key: 'reply',
      },
      forwardMsg: {
        label: (
          <div className={styles.rightButtonMenuItem}>
            <div>
              <img src={forwardIcon} />
              <span>转发消息</span>
            </div>
          </div>
        ),
        key: 'forwardMsg',
      },
      msgUp: {
        label: (
          <div className={styles.rightButtonMenuItem}>
            <div>
              <img src={msgUpIcon} />
              <span>置顶消息</span>
            </div>
          </div>
        ),
        key: 'msgUp',
      },
      msgUpClear: {
        label: (
          <div className={styles.rightButtonMenuItem}>
            <div>
              <img src={msgUpClearIcon} />
              <span>取消置顶</span>
            </div>
          </div>
        ),
        key: 'msgUpClear',
      },
      translateMsg: {
        label: (
          <div className={styles.rightButtonMenuItem}>
            <div>
              <img src={translateIcon} />
              <span>{'翻译'}</span>
            </div>
          </div>
        ),
        key: 'translateMsg',
      },
      clearTranslateMsg: {
        label: (
          <div className={styles.rightButtonMenuItem}>
            <div>
              <img src={translateCancelIcon} />
              <span>{'隐藏译文'}</span>
            </div>
          </div>
        ),
        key: 'clearTranslateMsg',
      },
      // forward: {
      //   label: (
      //     <div className={styles.rightButtonMenuItem}>
      //       <div>
      //         <img src={forwardIcon} />
      //         <span>转发子群</span>
      //       </div>
      //     </div>
      //   ),
      //   key: 'forward',
      // },
      // view: {
      //   label: (
      //     <div className={styles.rightButtonMenuItem}>
      //       <div>
      //         <img src={messageIcon} />
      //         <span>查看子群</span>
      //       </div>
      //     </div>
      //   ),
      //   key: 'view',
      // },
      // check: {
      //   label: (
      //     <div className={styles.rightButtonMenuItem}>
      //       <div>
      //         <img src={checkCircleIcon} style={{ margin: '0 12px 0 5px' }} />
      //          <span>多选</span>
      //       </div>
      //       <span>F</span>
      //     </div>
      //   ),
      //   key: 'check',
      // },
      // collect: {
      //   label: (
      //     <div className={styles.rightButtonMenuItem}>
      //       <div>
      //         <img src={collectIcon} />
      //         <span>收藏</span>
      //       </div>
      //       <span>A</span>
      //     </div>
      //   ),
      //   key: 'collect',
      // },
      // copyLink: {
      //   label: (
      //     <div className={styles.rightButtonMenuItem}>
      //       <div>
      //         <span>复制消息链接</span>
      //       </div>
      //     </div>
      //   ),
      //   key: 'copyLink',
      // },
      remove: {
        label: (
          <div className={styles.rightButtonMenuItem}>
            <div>
              <span>删除</span>
            </div>
          </div>
        ),
        key: 'remove',
      },
      revoke: {
        label: (
          <div className={styles.rightButtonMenuItem}>
            <div>
              <span>撤回</span>
            </div>
          </div>
        ),
        key: 'revoke',
      },
      divider: {
        type: 'divider',
      },
    };
    // if (!isThread && currentConversation?.groupID !== '') {
    //   tempMenu = Object.assign(tempMenu, {
    //     thread: {
    //       label: (
    //         <div className={styles.rightButtonMenuItem}>
    //           <div>
    //             <img src={messageIcon} />
    //             <span>在子群中回复</span>
    //           </div>
    //           {/* <span>T</span> */}
    //         </div>
    //       ),
    //       key: 'thread',
    //     },
    //   });
    // }
    return tempMenu;
  }, [message, conversationID, isThread]);

  const getCleanItems = (items: MenuProps['items'] = []) => {
    const filtered = items.filter(Boolean);

    const filteredItems = filtered.reduce<MenuProps['items']>((acc, curr) => {
      // 如果当前项为 divider 且上一个也是 divider，则跳过当前项
      if (
        curr?.type === 'divider' &&
        acc.length > 0 &&
        acc[acc.length - 1]?.type === 'divider'
      ) {
        return acc;
      }
      return [...acc, curr];
    }, []);
    // 如果最后一个元素是divider 就去掉最后一个元素
    if (
      filteredItems.length > 0 &&
      filteredItems[filteredItems.length - 1]?.type === 'divider'
    ) {
      filteredItems?.pop();
    }
    return filteredItems;
  };

  const getItems = useMemo(() => {
    let items: MenuProps['items'] = [];

    const translationFilter = (key: string) => {
      if (key === 'translateMsg' || key === 'clearTranslateMsg') {
        if (
          message.contentType !== MessageType.TextMessage &&
          message.contentType !== MessageType.QuoteMessage &&
          message.contentType !== MessageType.AtTextMessage &&
          message.contentType !== MessageType.MergeMessage
        ) {
          return false;
        }
        if (
          key === 'translateMsg' &&
          translationData?.translationState === 'finish'
        ) {
          return false;
        }
        if (
          key === 'clearTranslateMsg' &&
          (!translationData || translationData.translationState !== 'finish')
        ) {
          return false;
        }
      } else if (key === 'msgUp' || key === 'msgUpClear') {
        if (
          !message.groupID ||
          isExit ||
          message.status !== MessageStatus.Succeed
        ) {
          return false;
        }
        const currentMessageUpInfoIdList =
          currentMessageUpInfo?.map((i) => i.msgs?.clientMsgID) || [];
        if (currentMessageUpInfoIdList.includes(message.clientMsgID)) {
          if (key === 'msgUp') {
            return false;
          }
        } else if (key === 'msgUpClear') {
          return false;
        }
      }
      return true;
    };
    if (isMultiSession) {
      items = multiSessionMenus.map((key) => {
        if (
          key === 'translateMsg' &&
          translationData?.translationState === 'finish'
        ) {
          return;
        } else if (
          key === 'clearTranslateMsg' &&
          (!translationData || translationData.translationState !== 'finish')
        ) {
          return;
        }
        return menuButtons[key];
      });
    } else if (message.contentType === MessageType.CustomMessage) {
      let content: any = {};
      try {
        content = JSON.parse(message?.customElem?.data || '{}');
      } catch (e) {
        content = message?.customElem?.data;

        return null;
      }
      if (content?.type === 'stream') {
        items = menus.map((key) => {
          if (
            key === 'translateMsg' &&
            translationData?.translationState === 'finish'
          ) {
            return;
          } else if (
            key === 'clearTranslateMsg' &&
            (!translationData || translationData.translationState !== 'finish')
          ) {
            return;
          }
          if (key === 'remove' && isSender) {
            if (!isWithin24Hours(message.sendTime)) {
              return menuButtons.remove;
            }
            return menuButtons.revoke;
          }
          if (key === 'msgUp' || key === 'msgUpClear') {
            if (
              !message.groupID ||
              isExit ||
              message.status !== MessageStatus.Succeed
            ) {
              return false;
            }
            const currentMessageUpInfoIdList =
              currentMessageUpInfo?.map((i) => i.msgs?.clientMsgID) || [];
            if (currentMessageUpInfoIdList.includes(message.clientMsgID)) {
              if (key === 'msgUp') {
                return false;
              }
            } else if (key === 'msgUpClear') {
              return false;
            }
          }
          return menuButtons[key];
        });
      } else {
        items = robotMenus.map((key) => {
          if (!translationFilter(key)) {
            return;
          }
          if (key === 'remove' && isSender) {
            if (content?.type === 'clouddocument') {
              if (!isWithin24Hours(message.sendTime)) {
                return menuButtons.remove;
              }
              return menuButtons.revoke;
            } else {
              return menuButtons[key];
            }
          } else {
            return menuButtons[key];
          }
        });
      }
    } else if (message.contentType === MessageType.GroupAnnouncementUpdated) {
      items = groupAnnouncement.map((key) => {
        if (!translationFilter(key)) {
          return;
        }
        if (key === 'remove' && isSender) {
          if (!isWithin24Hours(message.sendTime)) {
            return menuButtons.remove;
          }
          return menuButtons.revoke;
        }
        return menuButtons[key];
      });
    } else if (showTreadBtnVisible) {
      items = threadMenus.map((key) => {
        if (
          message.contentType !== MessageType.TextMessage &&
          message.contentType !== MessageType.QuoteMessage &&
          message.contentType !== MessageType.MergeMessage &&
          message.contentType !== MessageType.AtTextMessage &&
          // eslint-disable-next-line eqeqeq
          key == 'copy'
        ) {
          return;
        }
        if (!translationFilter(key)) {
          return;
        }
        return menuButtons[key];
      });
    } else if (isSender) {
      items = myMenus.map((key) => {
        if (
          message.contentType !== MessageType.TextMessage &&
          message.contentType !== MessageType.QuoteMessage &&
          message.contentType !== MessageType.MergeMessage &&
          message.contentType !== MessageType.AtTextMessage &&
          // eslint-disable-next-line eqeqeq
          key == 'copy'
        ) {
          return;
        }
        if (!translationFilter(key)) {
          return;
        }
        // eslint-disable-next-line eqeqeq
        if (key == 'revoke') {
          if (!isWithin24Hours(message.sendTime)) {
            return menuButtons.remove;
          }
        }
        return menuButtons[key];
      });
    } else {
      items = menus.map((key) => {
        if (
          message.contentType !== MessageType.TextMessage &&
          message.contentType !== MessageType.QuoteMessage &&
          message.contentType !== MessageType.MergeMessage &&
          message.contentType !== MessageType.AtTextMessage &&
          // eslint-disable-next-line eqeqeq
          key == 'copy'
        ) {
          return;
        }
        if (!translationFilter(key)) {
          return;
        }
        return menuButtons[key];
      });
    }

    // 运行失败的机器人消息 屏蔽转发按钮
    if (message.contentType === MessageType.CustomMessage) {
      let content: any = {};
      try {
        content = JSON.parse(message?.customElem?.data || '{}');
      } catch (e) {
        content = message?.customElem?.data;
      }
      if (content?.type === 'stream' && content?.content?.error) {
        items = items.filter((i) => i?.key !== 'forwardMsg');
      }
    }

    // 发送失败时 文件图片类型不能转发
    if (
      message.status === MessageStatus.Failed &&
      (message.contentType === MessageType.PictureMessage ||
        message.contentType === MessageType.FileMessage)
    ) {
      items = items.filter((i) => i?.key !== 'forwardMsg');
    }

    // 消息的seq为0，即还未返回时，不允许引用、置顶
    if (
      message.status === MessageStatus.Sending ||
      (message.status === MessageStatus.Succeed && message.seq === 0)
    ) {
      items = items.filter((i) => i?.key !== 'reply' && i?.key !== 'msgUp');
    }

    // 群禁言时 或者 退出群聊时
    if (
      (currentGroupInfo?.status === GroupStatus.Muted &&
        currentMemberInGroup?.roleLevel === GroupMemberRole.Normal) ||
      isExit
    ) {
      items = items.filter(
        (i) => i?.key !== 'emoji' && i?.key !== 'reply' && i?.key !== 'revoke'
      );
    }
    return items;
  }, [
    message,
    isSender,
    showTreadBtnVisible,
    translationData,
    currentGroupInfo,
    isExit,
    currentMessageUpInfo,
  ]);

  const itemClick = ({ item, key, keyPath, domEvent }: any) => {
    switch (key) {
      case 'copy':
        handleCopy(message, !selectedTextRef.current);
        break;
      case 'revoke':
        tryRevoke();
        break;
      case 'remove':
        tryRemove();
        break;
      case 'view':
        openMessageColumn();
        break;
      case 'reply':
        replayMsg();
        break;
      case 'thread':
        openMessageColumn();
        break;
      case 'forwardMsg':
        openForwardModal();
        break;
      case 'translateMsg':
        tryTranslate();
        break;
      case 'clearTranslateMsg':
        clearTranslate();
        break;
      case 'msgUp':
        handleMsgUp();
        break;
      case 'msgUpClear':
        setClearUpConfirmModalVisible(true);
        break;
      default:
        break;
    }
    // 对消息有操作时，将该消息标为已读
    markConversationMessageAsRead(message);
    setRightBtnOpen(false);
  };

  const tryTranslate = async () => {
    updateTranslationState(message.clientMsgID, 'loading');
    const translateMsgReceived = ({ data }: any) => {
      updateTranslatedContent(message.clientMsgID, data.content);
      updateTranslationState(message.clientMsgID, 'finish');
      IMSDK.off(CbEvents.OnTranslateMsg, translateMsgReceived);
    };

    try {
      IMSDK.on(CbEvents.OnTranslateMsg, translateMsgReceived);
      await IMSDK.translateMsg({
        senderID: message.sendID,
        recvID: message.recvID,
        // content: message?.textElem?.content,
        content: getMessageContent(message),
        seq: message.seq,
        conversationID,
      });
    } catch (error) {
      IMSDK.off(CbEvents.OnTranslateMsg, translateMsgReceived);
      updateTranslationState(message.clientMsgID, 'finish');
      clearTranslate();
      feedbackToast({ error, msg: '翻译失败' });
    }
  };

  const clearTranslate = async () => {
    clearTranslation(message.clientMsgID);
  };

  const handleMsgUpConfirm = async () => {
    try {
      await IMSDK.setGroupUpMessages(message.groupID, message.seq);
      setConfirmModalVisible(false);
    } catch (error) {
      feedbackToast({ error, msg: '置顶失败' });
    }
  };

  const handleMsgUp = () => {
    if (currentMessageUpInfo.length > 0) {
      setConfirmModalVisible(true);
    } else {
      handleMsgUpConfirm();
    }
  };

  const handleMsgUpClear = async () => {
    try {
      await IMSDK.cancelGroupUpMessages(message.groupID, message.seq, true);
      setClearUpConfirmModalVisible(false);
    } catch (error) {
      feedbackToast({ error, msg: '取消置顶失败' });
    }
  };

  const tryRevoke = async () => {
    try {
      await IMSDK.revokeMessage({
        conversationID,
        clientMsgID: message.clientMsgID,
      });
      // updateOneMessage({
      //   ...message,
      //   contentType: MessageType.RevokeMessage,
      //   notificationElem: {
      //     detail: JSON.stringify({
      //       clientMsgID: message.clientMsgID,
      //       revokeTime: Date.now(),
      //       revokerID: selfUserID,
      //       revokerNickname: t('you'),
      //       revokerRole: 0,
      //       seq: message.seq,
      //       sessionType: message.sessionType,
      //       sourceMessageSendID: message.sendID,
      //       sourceMessageSendTime: message.sendTime,
      //       sourceMessageSenderNickname: message.senderNickname,
      //     }),
      //   },
      // });
    } catch (error) {
      feedbackToast({ error, msg: '撤回失败' });
    }
  };

  const tryRemove = async () => {
    try {
      await IMSDK.deleteMessage({
        clientMsgID: message.clientMsgID,
        conversationID,
      });
      deleteOneMessage(message.clientMsgID);
      if (message.groupID) {
        getCurrentMessageUpInfo(message.groupID);
      }
    } catch (error) {
      feedbackToast({ error, msg: '删除失败' });
    }
  };

  const openMessageColumn = async () => {
    await openThread({
      parentId: currentConversation?.groupID || '',
      startClientMsg: message,
    });
  };

  const replayMsg = () => {
    emit('REPLAYMSG', message);
  };

  return (
    <>
      <Menu items={getCleanItems(getItems)} onClick={itemClick} />
      {confirmModalVisible && (
        <ConfirmModal
          open={confirmModalVisible}
          title="提示"
          closeIcon={<img src={closeIcon} />}
          maskClosable={false}
          okText="确定"
          cancelText="取消"
          onCancel={() => setConfirmModalVisible(false)}
          onOk={handleMsgUpConfirm}
          centered={true}
          width={420}
        >
          <div className={styles.confirmText}>是否替换当前已有置顶消息</div>
        </ConfirmModal>
      )}
      {clearUpConfirmModalVisible && (
        <ConfirmModal
          open={clearUpConfirmModalVisible}
          title="提示"
          closeIcon={<img src={closeIcon} />}
          maskClosable={false}
          okText="取消置顶"
          cancelText="取消"
          onCancel={() => setClearUpConfirmModalVisible(false)}
          onOk={handleMsgUpClear}
          centered={true}
          width={420}
        >
          <div className={styles.confirmText}>
            取消置顶对全体群成员生效，是否取消置顶
          </div>
        </ConfirmModal>
      )}
    </>
  );
};
export default memo(RightButtonMenu);
