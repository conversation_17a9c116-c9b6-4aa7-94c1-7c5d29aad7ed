/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-statements */
/* eslint-disable max-lines */
/* eslint-disable indent */
import React, { FC, useRef, useEffect } from 'react';
import Mark from 'mark.js';
import { RenderMd } from '@/components/MdEditor';
import { useConversationStore } from '@/store';
import { Space } from '@ht/sprite-ui';
import useUserInfo from '@/hooks/useUserInfo';
import { useSendMessage } from '@/hooks/useSendMessage';
import copyIcon from '@/assets/stream/copy.png';
import refreshIcon from '@/assets/stream/refresh.png';
import { handleCopy } from '@/utils/message';
import { AILoadingText } from '@/utils/constants';
import { useHistoryMessageList } from '@/hooks/useHistoryMessageList';
import { useTranslationStore } from '@/store/translationStore';
import TranslationRender from '../TranslationRender';
import { IMessageItemProps } from '..';
import NetworkContent from './NetworkContent';
import KnowledgeContent from './KnowledgeContent';
import ActionButtons from './ActionButton';
import Loading from './Loading';
import Cost from './Cost';
import styles from './index.less';

interface StreamMessageProps extends IMessageItemProps {
  content: {
    content: ContentType;
    event?: string;
  };
}

interface ContentType {
  id: string;
  start: number;
  end: number;
  answer?: string;
  input_tokens: number;
  output_tokens: number;
  latency: number;
  think_message?: {
    answer: string;
  };
  knowledge_retrieve?: {
    start: number;
    end: number;
    latency: number;
    results: Array<{
      index: number;
      score: number;
      doc_name: string;
      doc_type: number;
      doc_url: string;
      content: string;
    }>;
  };
  qa_retrieve?: {
    start: number;
    end: number;
    latency: number;
    results: Array<{
      index: number;
      score: number;
      question: string;
      answer: string;
    }>;
  };
  network_search?: {
    start: number;
    end: number;
    latency: number;
    results: Array<{
      index: number;
      title: string;
      url: string;
      content: string;
    }>;
  };
  error?: {
    code: string;
    msg: string;
  };
  feedback?: number;
}

// 获取按钮配置的函数
const getActionButtons = ({
  message,
  isLastMsg,
  handleRefresh,
  isError = false,
}: {
  messageContent: ContentType;
  message: any;
  handleRefresh: () => void;
  isError?: boolean;
  isLastMsg?: boolean;
}) => {
  // 否则根据是否是最后一条消息来决定显示哪些按钮
  return [
    // 只有当不是错误消息时才显示复制按钮
    ...(isError
      ? []
      : [
          {
            key: 'copy',
            title: '复制',
            icon: <img src={copyIcon} alt="copy" />,
            onClick: () => {
              handleCopy(message, true);
            },
          },
        ]),
    // 只有最后一条消息才显示重新生成按钮
    ...(isLastMsg
      ? [
          {
            key: 'refresh',
            title: '重新生成',
            icon: <img src={refreshIcon} alt="refresh" />,
            onClick: handleRefresh,
          },
        ]
      : []),
  ];
};

/**
 * 处理消息内容中的图片URL替换
 * @param content 原始消息内容
 * @returns 处理后的消息内容
 */
export const processImageUrls = (content: string): string => {
  if (typeof content !== 'string') {
    return content;
  }
  // 测试环境将eiplite替换为eipsit
  if (location.host === 'eipsit.htsc.com.cn') {
    return content.replace(
      /eiplite\.htsc\.com\.cn\/llmpf/g,
      'eipsit.htsc.com.cn/llmops'
    );
  }
  // 生产环境eipnew替换为当前域名
  else if (['eip.htsc.com.cn', 'eiplite.htsc.com.cn'].includes(location.host)) {
    return content.replace(
      /eipnew\.htsc\.com\.cn\/llmpf/g,
      `${location.host}/llmpf`
    );
  }
  return content;
};

const StreamMessageRender: FC<StreamMessageProps> = (props) => {
  const {
    content,
    message,
    prevMsg,
    isLastMsg,
    isSearch,
    conversationID,
    searchValue = '',
    isForwardMessage,
  } = props;
  const { content: messageContent, event } = content;

  const containerRef = useRef<HTMLDivElement>(null);
  const markInstanceRef = useRef<any>(null);

  const currentConversation = useConversationStore(
    (state) => state.currentConversation
  );
  const currentMultiSession = useConversationStore(
    (state) => state.currentMultiSession
  );
  const isMultiSession = useConversationStore((state) => state.isMultiSession);
  const changeRightArea = useConversationStore(
    (state) => state.changeRightArea
  );
  const { userDetail } = useUserInfo(currentConversation?.userID);
  const { sendStreamMessage } = useSendMessage(
    isMultiSession ? currentMultiSession : currentConversation
  );

  // 使用钩子获取翻译数据，只有当相关数据变化时才会触发重新渲染
  const translationData = useTranslationStore((state) =>
    message.clientMsgID ? state.translations[message.clientMsgID] : undefined
  );

  useEffect(() => {
    if (containerRef.current) {
      markInstanceRef.current = new Mark(containerRef.current);
    }
  }, []);

  useEffect(() => {
    if (markInstanceRef.current) {
      markInstanceRef.current.unmark();
      if (searchValue.trim() && isSearch) {
        markInstanceRef.current.mark(searchValue.trim(), {
          className: styles.search_Highlight,
        });
      }
    }
  }, [isSearch, searchValue]);

  const handleRefresh = () => {
    sendStreamMessage({
      recvUser: userDetail,
      curMsg: message,
      lastMsg: prevMsg,
    });
  };

  if (messageContent?.error) {
    const errorContent =
      messageContent.error.code === '999'
        ? '抱歉，系统开小差了，请稍后重试'
        : messageContent.error.msg;
    return (
      <div className={styles.streamMessage}>
        <div className={styles.errorContent}>{errorContent}</div>
        <div className={styles.errorFooter}>
          <div className={styles.actionButtonWrapper}>
            <ActionButtons
              message={message}
              onRefresh={handleRefresh}
              buttons={getActionButtons({
                messageContent,
                message,
                isLastMsg,
                handleRefresh,
                isError: true,
              })}
            />
          </div>
          <span className={styles.errorStatus}>运行失败</span>
        </div>
      </div>
    );
  }

  return (
    <Space direction="vertical" size={8} className={styles.messageContainer}>
      {/* {messageContent?.think_message && (
        <ThinkContent content={messageContent.think_message.answer} />
      )} */}
      {messageContent?.answer && (
        <div ref={containerRef}>
          {messageContent.answer === AILoadingText ? (
            <Loading />
          ) : (
            <div className={styles.fadeInContent}>
              <RenderMd
                id={message.clientMsgID}
                value={processImageUrls(messageContent.answer)}
                hasKnowledgeRetrieve={
                  !!messageContent?.knowledge_retrieve?.results
                }
                hasNetworkSearch={!!messageContent?.network_search?.results}
                referenceClick={(val: number, type: 'know' | 'network') => {
                  const idx = val - 1 >= 0 ? val - 1 : 0;
                  const data =
                    type === 'know'
                      ? messageContent?.knowledge_retrieve?.results
                      : messageContent?.network_search?.results;
                  const obj = data?.[idx] ? data[idx] : undefined;
                  changeRightArea(
                    type === 'know'
                      ? 'OPEN_ROBOT_ANSWER_SOURCE'
                      : 'OPEN_ROBOT_ONLINE_SEARCE',
                    {
                      data,
                      activeObj: obj,
                    }
                  );
                }}
              />
            </div>
          )}
        </div>
      )}

      {!isSearch && !isForwardMessage && (
        <>
          {/* 添加翻译组件 */}
          {translationData && (
            <TranslationRender
              translationData={translationData}
              messageId={message.clientMsgID}
            />
          )}

          {messageContent?.knowledge_retrieve?.results && (
            <KnowledgeContent content={messageContent.knowledge_retrieve} />
          )}

          {messageContent?.network_search?.results && (
            <NetworkContent
              content={{
                results: messageContent.network_search.results,
              }}
            />
          )}

          {!!messageContent?.end && (
            <div className={styles.actionFooter}>
              <ActionButtons
                message={message}
                onRefresh={handleRefresh}
                onFeedback={(type) => {
                  // TODO: 点赞点踩逻辑，需根据实际业务实现
                }}
                buttons={getActionButtons({
                  messageContent,
                  message,
                  isLastMsg,
                  handleRefresh,
                })}
              />
              <Cost
                content={{
                  latency: messageContent.latency,
                  input_tokens: messageContent.input_tokens,
                  output_tokens: messageContent.output_tokens,
                }}
              />
            </div>
          )}

          {/* {message.clientMsgID === clientMsgId &&
            questions &&
            !!messageContent?.end && (
              <GuideQuestions
                questions={questions}
                currentConversation={currentConversation}
              />
            )} */}
        </>
      )}
    </Space>
  );
};

export default StreamMessageRender;
