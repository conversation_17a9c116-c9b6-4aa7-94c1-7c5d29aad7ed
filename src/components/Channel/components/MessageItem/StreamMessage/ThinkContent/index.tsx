import React, { <PERSON> } from 'react';
import { Card, Typography, Space } from 'antd';
import { BulbOutlined } from '@ant-design/icons';

interface ThinkContentProps {
  content:
    | string
    | {
        answer: string;
      };
}

const ThinkContent: FC<ThinkContentProps> = ({ content }) => {
  let thinkText = '';

  if (typeof content === 'string') {
    thinkText = content;
  } else if (content && typeof content === 'object' && 'answer' in content) {
    thinkText = content.answer;
  }

  if (!thinkText) {
    return null;
  }

  return (
    <Card
      title={
        <Space>
          <BulbOutlined /> 思考过程
        </Space>
      }
      size="small"
      style={{ width: '100%', backgroundColor: '#f9f9f9' }}
    >
      <Typography.Paragraph
        style={{
          whiteSpace: 'pre-wrap',
          color: '#666',
          fontStyle: 'italic',
          margin: 0,
        }}
      >
        {thinkText}
      </Typography.Paragraph>
    </Card>
  );
};

export default ThinkContent;
