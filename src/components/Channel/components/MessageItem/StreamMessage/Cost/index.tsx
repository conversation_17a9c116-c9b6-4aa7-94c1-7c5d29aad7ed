import React, { <PERSON> } from 'react';
import styles from './index.less';

interface CostProps {
  content: {
    latency: number;
    input_tokens: number;
    output_tokens: number;
  };
}

const Cost: FC<CostProps> = ({ content }) => {
  if (!content) {
    return null;
  }

  const { latency, input_tokens, output_tokens } = content;
  const totalTokens = input_tokens + output_tokens;

  return (
    <div className={styles.costContainer}>
      <div className={styles.costItem}>
        <span className={styles.value}>{latency?.toFixed(2)}s</span>
      </div>
      <div className={styles.divider}>|</div>
      <div className={styles.costItem}>
        <span className={styles.value}>{totalTokens}&nbsp;Tokens</span>
      </div>
    </div>
  );
};

export default Cost;
