import React, { FC, useState, useEffect } from 'react';
import { message as Message } from '@ht/sprite-ui';
import ImgPreviewModal from '@/components/ImgPreviewModal';
import hideIcon from '@/assets/images/messageItem/hideIcon.png';
import classNames from 'classnames';
import { IMessageItemProps } from '.';
import UserInfoRender from './UserInfoRender';
import styles from './index.less';

const PictureMessageRender: FC<IMessageItemProps> = ({ ...props }) => {
  const { message, showName } = props;
  const { pictureElem } = message;
  const { url = '', uuid } = pictureElem?.sourcePicture || {};
  const fileName = uuid?.split('/')[1] || 'image.png';
  const [open, setOpen] = useState<boolean>(false);

  const download = async () => {
    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('网络响应失败');
      }
      const blob = await response.blob();
      const blobUrl = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      URL.revokeObjectURL(blobUrl);
    } catch (error) {
      Message.error('下载失败');
    }
  };

  const openView = async () => {
    window.open(url, '_blank');
    // try {
    //   const response = await fetch(url);
    //   if (!response.ok) {
    //     throw new Error('网络响应失败');
    //   }
    //   const blob = await response.blob();
    //   const blobUrl = URL.createObjectURL(blob);
    //   window.open(blobUrl, '_blank');
    // } catch (error) {
    //   console.error('openView', error);
    // }
  };
  const calcBestWidthHeight = (
    width: number | undefined,
    height: number | undefined
  ) => {
    const size = {
      width: 420,
      height: 300,
    };

    if (!width || !height) {
      return size;
    }
    try {
      const aspectRatio = width / height;
      const aspectWidth = 300 * aspectRatio;
      // 气泡最小宽度458，减去padding 20
      if (width <= 438 && height <= 300) {
        return { width, height };
      } else if (aspectRatio >= 2 || aspectWidth > 438) {
        return {
          width: 438,
          height: 438 / aspectRatio,
        };
      } else {
        return {
          width: aspectWidth,
          height: 300,
        };
      }
    } catch (e) {}

    return size;
  };

  return (
    <div className={styles.pictureWarp}>
      <UserInfoRender {...props}>
        <div>
          <div
            style={{
              ...calcBestWidthHeight(
                pictureElem?.bigPicture.width,
                pictureElem?.bigPicture.height
              ),
            }}
          >
            <img
              className={styles.showPicture}
              src={pictureElem?.sourcePicture.url}
              onClick={() => setOpen(true)}
            />
          </div>
        </div>
      </UserInfoRender>
      {open && (
        <ImgPreviewModal
          open={open}
          onClose={() => setOpen(false)}
          download={download}
          imgMsg={message}
          fileName={fileName}
          showName={showName}
          openView={openView}
          // imgInfo={{
          //   ...pictureElem?.sourcePicture,
          //   fileName,
          // }}
        />
      )}
    </div>
  );
};

export default PictureMessageRender;
