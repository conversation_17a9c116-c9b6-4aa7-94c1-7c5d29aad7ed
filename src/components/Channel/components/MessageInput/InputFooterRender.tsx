/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-lines */
/* eslint-disable indent */
import { FC, useEffect, useState } from 'react';
import classNames from 'classnames';
import _ from 'lodash';
import { Popover, Tooltip, Upload } from '@ht/sprite-ui';
import { RightOutlined } from '@ht-icons/sprite-ui-react';
import fileIcon from '@/assets/channel/messageInput/file.svg';
import fileHoverIcon from '@/assets/channel/messageInput/fileHover.svg';
import showFormaterIcon from '@/assets/channel/messageInput/showFormater.svg';
import hideFormaterIcon from '@/assets/channel/messageInput/hideFormater.svg';
import emojiIcon from '@/assets/channel/messageInput/emoji.svg';
import atIcon from '@/assets/channel/messageInput/at.svg';
import audioIcon from '@/assets/channel/messageInput/audio.png';
import screenshotIcon from '@/assets/channel/messageInput/screenshot2.svg';
import videoIcon from '@/assets/channel/messageInput/video.png';
import cloudDocIcon from '@/assets/channel/messageInput/cloudDoc.png';
import localUploadIcon from '@/assets/channel/messageInput/localUpload.png';
import localUploadActiveIcon from '@/assets/channel/messageInput/localUploadActive.png';
import { invoke } from '@tauri-apps/api/core';
import {
  register,
  isRegistered,
  unregister,
} from '@tauri-apps/plugin-global-shortcut';
import CloudDocSelect from '@/components/CloudDocSelect';
import EmojiComponent from './EmojiComponent';
import styles from './index.less';

declare global {
  interface Window {
    __TAURI_INTERNALS__: any;
  }
}
// const uploadArr = ['video', 'audio'];

const switchType = (type: string) => {
  switch (type) {
    case 'audio':
      return 'audio/*';
    case 'video':
      return 'video/*';
    case 'file':
      return '*';
    default:
      return '*';
  }
};

interface InputFooterRenderProps {
  showFormater: boolean;
  changShowFormater: (val: boolean) => void;
  customRequest?: (data: any, type: string) => void;
  insertEmoji: (val: string) => void;
  shouldHideOperateList?: number[]; // 需要隐藏的操作idx
  pasteContainerRef: React.RefObject<any>;
  conversationID?: string;
  cloudUpload?: (data: any) => void;
  disabledBtn?: boolean;
  isGroup?: boolean;
}

type allExtendOperateListProps = {
  idx?: number;
  title?: string;
  icon?: string;
  type: string;
};

const InputFooterRender: FC<InputFooterRenderProps> = ({
  showFormater = true,
  shouldHideOperateList = [],
  changShowFormater,
  customRequest,
  insertEmoji,
  pasteContainerRef,
  conversationID,
  cloudUpload,
  disabledBtn = false,
  isGroup = true,
}) => {
  const [fileOpen, setFileOpen] = useState<boolean>(false);
  const [open, setOpen] = useState<boolean>(false);
  const [cloudOpen, setCloudOpen] = useState<boolean>(false);
  const allExtendOperateList: allExtendOperateListProps[] = [
    {
      idx: 0,
      title: '文件',
      icon: fileIcon,
      type: 'file',
    },
    {
      idx: 1,
      title: showFormater ? '隐藏格式' : '显示格式',
      icon: showFormater ? showFormaterIcon : hideFormaterIcon,
      type: 'showOrHiddenFormater',
    },
    {
      idx: 2,
      title: '表情',
      icon: emojiIcon,
      type: 'emoji',
    },
    // {
    //   idx: 5,
    //   title: '音频',
    //   icon: audioIcon,
    //   type: 'audio',
    // },
  ];

  if (isGroup) {
    allExtendOperateList.push({
      idx: 3,
      title: '提及某个成员',
      icon: atIcon,
      type: 'at',
    });
  }
  if (window.__TAURI_INTERNALS__) {
    allExtendOperateList.push(
      {
        type: 'divider',
      },
      {
        idx: 4,
        title: '截图（Alt+A）',
        icon: screenshotIcon,
        type: 'screenshot',
      }
    );
  }

  useEffect(() => {
    initShortcut();
  }, []);

  const initShortcut = async () => {
    if (!window.__TAURI_INTERNALS__) {
      return;
    }
    const isKeyBindRegistered = await isRegistered('Alt+A');
    if (isKeyBindRegistered) {
      await unregister('Alt+A');
    }
    await register('Alt+A', (e) => {
      if (e.state === 'Released') {
        handleScreenshot();
      }
    });
  };

  const handleScreenshot = async () => {
    const screenshotResult: any = await invoke('launch_screenshot_app');
    if (screenshotResult?.code === 1) {
      pasteContainerRef.current?.focus();
      setTimeout(() => {
        invoke('send_Ctrl_V');
      }, 100);
    }
  };
  const extendOperateList = _.filter(
    allExtendOperateList,
    (listItem) => !_.includes(shouldHideOperateList, listItem.idx)
  );

  const extendOperateClick = async (type: string) => {
    if (disabledBtn) {
      return;
    }
    switch (type) {
      case 'showOrHiddenFormater':
        localStorage.setItem(
          'linkim.messageInput.formatterDisplayPref',
          JSON.stringify(!showFormater)
        );

        changShowFormater(!showFormater);
        break;
      case 'at':
        insertAt();
        break;
      case 'screenshot':
        handleScreenshot();
        break;
      default:
    }
  };

  const insertAt = () => {
    insertEmoji('@');
  };

  return (
    <div className={styles.extendOperate}>
      {extendOperateList.map((item: any, index: number) => {
        if (item.type && item.type === 'divider') {
          return (
            // eslint-disable-next-line react/no-array-index-key
            <div key={`divider-${index}`} className={styles.line}></div>
          );
        } else if (item.type && item.type === 'emoji') {
          return (
            <EmojiComponent
              key={item.idx}
              handleEmojiSelect={(emoji: any) => {
                insertEmoji(emoji.native);
              }}
              disabledBtn={disabledBtn}
            >
              <div
                onMouseDown={(e) => e.preventDefault()}
                className={classNames(
                  styles.operateItem,
                  disabledBtn && styles.operateItemdisabled
                )}
              >
                <Tooltip
                  title={item.title ? item.title : null}
                  overlayClassName={styles.tooltipWrap}
                  overlayStyle={disabledBtn ? { display: 'none' } : {}}
                >
                  <img src={item.icon} alt={item.title} />
                </Tooltip>
              </div>
            </EmojiComponent>
          );
        }
        // else if (uploadArr.includes(item.type)) {
        //   return (
        //     <div
        //       key={item.idx}
        //       // onClick={item?.onClick}
        //       onMouseDown={(e) => e.preventDefault()}
        //       className={styles.operateItem}
        //     >
        //       <Upload
        //         accept={switchType(item.type)}
        //         key={item.idx}
        //         action={''}
        //         customRequest={(data) => customRequest?.(data, item.type)}
        //         showUploadList={false}
        //       >
        //         <Tooltip
        //           title={item.title ? item.title : null}
        //           overlayClassName={styles.tooltipWrap}
        //         >
        //           <img src={item.icon} alt={item.title} />
        //         </Tooltip>
        //       </Upload>
        //     </div>
        //   );
        // }
        else if (item.type === 'file') {
          return (
            <Popover
              key={item.idx}
              open={fileOpen}
              onOpenChange={(val: boolean) => {
                if (disabledBtn) {
                  return;
                }
                if (val) {
                  setOpen(false);
                }
                setFileOpen(val);
              }}
              trigger={'click'}
              placement="topLeft"
              overlayClassName={styles.filePopoverWarp}
              content={
                <div className={styles.filePopoverContent}>
                  <Popover
                    trigger={'hover'}
                    placement="right"
                    open={cloudOpen}
                    content={
                      <CloudDocSelect
                        conversationID={conversationID!}
                        onChange={(val: any) => {
                          setCloudOpen(false);
                          cloudUpload?.(val);
                          setFileOpen(false);
                        }}
                      />
                    }
                    destroyTooltipOnHide={true}
                    onOpenChange={(val: boolean) => setCloudOpen(val)}
                    overlayClassName={styles.cloudDocSelectPopoverWarp}
                  >
                    <div className={styles.item}>
                      <div className={styles.desc}>
                        <img src={cloudDocIcon} className={styles.show} />
                        <span>发送云文档</span>
                      </div>
                      <RightOutlined />
                    </div>
                  </Popover>
                  <Upload
                    accept={switchType(item.type)}
                    key={item.idx}
                    action={''}
                    customRequest={(data) => {
                      customRequest?.(data, item.type);
                      setFileOpen(false);
                    }}
                    showUploadList={false}
                  >
                    <div className={styles.item}>
                      <div className={styles.desc}>
                        <img src={localUploadIcon} className={styles.show} />
                        <span>发送本地文件</span>
                      </div>
                    </div>
                  </Upload>
                </div>
              }
            >
              <Tooltip
                title={item.title}
                overlayClassName={styles.tooltipWrap}
                trigger={'hover'}
                onOpenChange={(val: boolean) => {
                  if (!fileOpen) {
                    setOpen(val);
                  }
                }}
                open={open}
                overlayStyle={disabledBtn ? { display: 'none' } : {}}
              >
                <div
                  key={item.idx}
                  className={classNames(
                    styles.operateItem,
                    disabledBtn && styles.operateItemdisabled,
                    item.type === 'file' && styles.operateFileItem
                  )}
                >
                  <img
                    src={item.icon}
                    alt={item.title}
                    className={classNames(
                      fileOpen && styles.rotate,
                      styles.itemIcon
                    )}
                  />
                  {item.type === 'file' && (
                    <img
                      src={fileHoverIcon}
                      alt={item.title}
                      className={styles.fileHoverIcon}
                    />
                  )}
                </div>
              </Tooltip>
            </Popover>
          );
        } else {
          return (
            <div
              key={item.idx}
              onClick={() => extendOperateClick(item.type)}
              onMouseDown={(e) => e.preventDefault()}
              className={classNames(
                styles.operateItem,
                disabledBtn && styles.operateItemdisabled
              )}
            >
              <Tooltip
                title={item.title ? item.title : null}
                overlayClassName={styles.tooltipWrap}
                overlayStyle={disabledBtn ? { display: 'none' } : {}}
              >
                <img src={item.icon} alt={item.title} />
              </Tooltip>
            </div>
          );
        }
      })}
    </div>
  );
};
export default InputFooterRender;
