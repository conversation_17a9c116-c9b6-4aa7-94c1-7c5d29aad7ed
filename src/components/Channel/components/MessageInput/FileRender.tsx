/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable indent */
import { FC, useEffect, useRef, useState } from 'react';
import { Tooltip } from '@ht/sprite-ui';
import { CloseCircleFilled } from '@ht-icons/sprite-ui-react';
import ImgPreviewModal from '@/components/ImgPreviewModal';
import zipIcon from '@/assets/channel/messageInput/fileRender/zip.png';
import fileIcon from '@/assets/channel/messageInput/fileRender/file.png';
import arrowIcom from '@/assets/channel/messageInput/fileRender/arrow.svg';
import dayjs from 'dayjs';
import { getDocIcon, docTypeEnum } from '@/utils/utils';
import { FileListType } from '.';
import styles from './index.less';

export const getFileIcon = (fileName: string, type: 'icon' | 'text') => {
  let result;
  const idx = fileName.lastIndexOf('.');
  const fileType = fileName.slice(idx + 1).toLowerCase();
  switch (fileType) {
    case 'doc':
      result = type === 'icon' ? getDocIcon(docTypeEnum.WORD) : 'Word 文档';
      break;
    case 'docx':
      result = type === 'icon' ? getDocIcon(docTypeEnum.WORD) : 'Word 文档';
      break;
    case 'xls':
      result =
        type === 'icon' ? getDocIcon(docTypeEnum.EXCEL) : 'Excel 电子表格';
      break;
    case 'csv':
      result =
        type === 'icon' ? getDocIcon(docTypeEnum.EXCEL) : 'Excel 电子表格';
      break;
    case 'xlsx':
      result =
        type === 'icon' ? getDocIcon(docTypeEnum.EXCEL) : 'Excel 电子表格';
      break;
    case 'ppt':
      result = type === 'icon' ? getDocIcon(docTypeEnum.PPT) : 'PPT';
      break;
    case 'pptx':
      result = type === 'icon' ? getDocIcon(docTypeEnum.PPT) : 'PPT';
      break;
    case 'pdf':
      result = type === 'icon' ? getDocIcon(docTypeEnum.PDF) : 'PDF';
      break;
    case 'xmind':
      result = type === 'icon' ? getDocIcon(docTypeEnum.MINDMAPNEW) : 'xmind';
      break;
    case 'md':
      result = type === 'icon' ? getDocIcon(docTypeEnum.MARKDOWN) : 'md';
      break;
    case 'zip':
      result = type === 'icon' ? zipIcon : '压缩包';
      break;
    case 'rar':
      result = type === 'icon' ? zipIcon : '压缩包';
      break;
    default:
      result = type === 'icon' ? fileIcon : fileType;
  }
  return result;
};

interface FileRenderProps {
  fileList: FileListType[];
  setFileList: (value: FileListType[]) => void;
}

const FileRender: FC<FileRenderProps> = ({ fileList, setFileList }) => {
  const [open, setOpen] = useState<boolean>(false);
  const [fileData, setFileData] = useState<any>();
  const [hasScroll, setHasScroll] = useState<boolean>(false);
  const [showScroll, setShowScroll] = useState<'left' | 'right' | 'all'>(
    'right'
  );
  const fileRef = useRef<any>(null);

  useEffect(() => {
    setShowScroll('right');
  }, []);

  useEffect(() => {
    const observer = new ResizeObserver((entries) => {
      entries.forEach((entry) => {
        const hasScroll = entry.target.scrollWidth > entry.target.clientWidth;
        setHasScroll(hasScroll);
        if (!hasScroll) {
          setShowScroll('right');
        }
      });
    });
    if (fileRef.current) {
      observer.observe(fileRef.current);
    }

    return () => {
      if (fileRef.current) {
        observer.unobserve(fileRef.current);
      }
    };
  }, [fileList]);

  const previewImg = async (data: FileListType) => {
    const { width, height } = await getPicInfo(data.url);
    setFileData({
      width,
      height,
      url: data.url,
      fileName: data?.file?.name || '',
    });
    setOpen(true);
  };

  const getPicInfo = (url: string): Promise<HTMLImageElement> =>
    new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = function () {
        resolve(img);
      };
      img.src = url;
    });

  const removeItem = (data: FileListType) => {
    const newFileData = fileList.filter((item) => item.id !== data.id);
    setFileList(newFileData);
  };

  const leftArrow = () => {
    const nowScroll = fileRef.current.scrollLeft;
    if (nowScroll - 200 <= 0) {
      fileRef.current.scrollTo({
        left: 0,
        behavior: 'smooth',
      });
      setShowScroll('right');
    } else {
      fileRef.current.scrollTo({
        left: nowScroll - 200,
        behavior: 'smooth',
      });
      setShowScroll('all');
    }
  };

  const rightArrow = () => {
    const nowScroll = fileRef.current.scrollLeft;
    const { scrollWidth, clientWidth } = fileRef.current;
    if (nowScroll + 200 >= scrollWidth - clientWidth) {
      fileRef.current.scrollTo({
        left: scrollWidth,
        behavior: 'smooth',
      });
      setShowScroll('left');
    } else {
      fileRef.current.scrollTo({
        left: nowScroll + 200,
        behavior: 'smooth',
      });
      setShowScroll('all');
    }
  };

  return (
    <div className={styles.fileRenderWarp}>
      <div className={styles.fileRenderContent} ref={fileRef}>
        {fileList.map((item) => {
          if (item.isImage) {
            return (
              <div
                key={item.id}
                className={styles.fileImgItem}
                onClick={() => previewImg(item)}
              >
                <div>
                  <img src={item.url} />
                </div>
                <Tooltip title={'移除文件'}>
                  <div
                    className={styles.closeIocn}
                    onClick={(e) => {
                      e.stopPropagation();
                      removeItem(item);
                    }}
                  >
                    <CloseCircleFilled />
                  </div>
                </Tooltip>
              </div>
            );
          } else if (item.isClouDoc) {
            return (
              <div className={styles.cloudDocItm} key={item.id}>
                <div className={styles.icon}>
                  <img src={getDocIcon(item.docInfo.documentType)} />
                </div>
                <div className={styles.fileInfo}>
                  <div
                    className={styles.fileName}
                    title={item.docInfo.documentName}
                  >
                    {item.docInfo.documentName}
                  </div>
                  <div className={styles.info}>
                    <span>创建者:</span>
                    <span>{item.docInfo.createdBy}</span>
                    <div className={styles.line}></div>
                    <span>创建时间:</span>
                    <span
                      style={{ textOverflow: 'ellipsis', overflow: 'hidden' }}
                      title={dayjs(item.docInfo.createdTime).format(
                        'YYYY-MM-DD HH:mm'
                      )}
                    >
                      {dayjs(item.docInfo.createdTime).format(
                        'YYYY-MM-DD HH:mm'
                      )}
                    </span>
                  </div>
                </div>
                <Tooltip title={'移除文件'}>
                  <div
                    className={styles.closeIocn}
                    onClick={(e) => {
                      e.stopPropagation();
                      removeItem(item);
                    }}
                  >
                    <CloseCircleFilled />
                  </div>
                </Tooltip>
              </div>
            );
          } else {
            return (
              <div className={styles.fileItem} key={item.id}>
                <div className={styles.icon}>
                  <img src={getFileIcon(item?.file?.name || '', 'icon')} />
                </div>
                <div className={styles.fileInfo}>
                  <div className={styles.fileName} title={item?.file?.name}>
                    {item?.file?.name}
                  </div>
                  <div className={styles.info}>
                    {getFileIcon(item?.file?.name || '', 'text')}
                  </div>
                </div>
                <Tooltip title={'移除文件'}>
                  <div
                    className={styles.closeIocn}
                    onClick={(e) => {
                      e.stopPropagation();
                      removeItem(item);
                    }}
                  >
                    <CloseCircleFilled />
                  </div>
                </Tooltip>
              </div>
            );
          }
        })}
      </div>
      {hasScroll && (showScroll === 'left' || showScroll === 'all') && (
        <div className={styles.leftArrow}>
          <img src={arrowIcom} onClick={leftArrow} />
        </div>
      )}
      {hasScroll && (showScroll === 'right' || showScroll === 'all') && (
        <div className={styles.rightArrow}>
          <img src={arrowIcom} onClick={rightArrow} />
        </div>
      )}
      {open && (
        <ImgPreviewModal
          open={open}
          onClose={() => setOpen(false)}
          download={() => {}}
          imgInfo={fileData}
          isInput={true}
          fileName={fileData?.fileName || ''}
          openView={() => {}}
        />
      )}
    </div>
  );
};

export default FileRender;
