import { FC } from 'react';
import dayjs from 'dayjs';
import { MessageItem as MessageItemType } from '@ht/openim-wasm-client-sdk';
import { getAvatarUrlFromMessage } from '@/utils/avatar';
import OIMAvatar from '../OIMAvatar';
import styles from './index.less';

const getTime = (data: number) => {
  const nowTiem = dayjs().format('YYYY/M/D');
  const senderTime = dayjs(data).format('YYYY/M/D');
  const diffDay = dayjs().diff(dayjs(data), 'days');
  if (nowTiem === senderTime) {
    const time = dayjs(data).format('HH:mm');
    return `今天：${time}`;
  } else {
    return `${diffDay === 0 ? 1 : diffDay}天前`;
  }
};

interface FileHeaderProps {
  message: MessageItemType;
  showName: string;
  fileName?: string;
}

const FileHeader: FC<FileHeaderProps> = ({ message, showName, fileName }) => {
  const name = message.fileElem?.fileName || fileName || '';
  return (
    <div className={styles.fileHeaderWarp}>
      <div>
        <OIMAvatar
          userID={message?.sendID}
          hideOnlineStatus={true}
          src={getAvatarUrlFromMessage(message)}
          size={36}
          shape="square"
        />
      </div>
      <div className={styles.info}>
        <div className={styles.senderNickname}>{message.senderNickname}</div>
        <div>
          <span>{getTime(message.sendTime)} </span>
          <span>
            在 {message.groupID ? '#' : ''}
            {showName} 中——{name}
          </span>
        </div>
      </div>
    </div>
  );
};
export default FileHeader;
