import { FC, useEffect, useMemo, useState } from 'react';
import dayjs from 'dayjs';
import _ from 'lodash';
import classNames from 'classnames';
import { message, Modal, Tooltip } from '@ht/sprite-ui';
import { IMSDK } from '@/layouts/BasicLayout';
import {
  ConversationItem,
  EmployeeItem,
  GroupMemberItem,
  SessionType,
  GroupMemberRole,
  GroupItem,
} from '@ht/openim-wasm-client-sdk';
import { feedbackToast } from '@/utils/common';
import { t } from 'i18next';
import { useCopyToClipboard } from 'react-use';
import { useUserStore, useConversationStore } from '@/store';
import copyIcon from '@/assets/channel/rightButtonMenu/copy.png';
import { RenderMd } from '@/components/MdEditor';
import EditNameModal, { ModalType } from '../EditNameModal';
import GroupAnnouncementModal from '../GroupAnnouncementModal';
import styles from './index.less';

interface SettingDetailsProps {
  conversationInfo: ConversationItem | undefined;
  groupInfo: GroupItem | undefined;
  isGroup: boolean;
  handleCancel: () => void;
}

const SettingDetails: FC<SettingDetailsProps> = ({
  conversationInfo,
  isGroup,
  groupInfo,
  handleCancel,
}) => {
  const { showName, conversationID, groupID = '' } = conversationInfo || {};
  const {
    ownerUserID = '',
    createTime = '',
    notification = '',
    creatorUserID,
  } = groupInfo || {};

  const { userID: selfUserID } = useUserStore.getState().selfInfo;
  const [ownerInfo, setOwnerInfo] = useState<GroupMemberItem | null>(null);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [visible, setVisible] = useState<boolean>(false);
  const [modalType, setModalType] = useState<ModalType>('groupName');
  const [_i, copyToClipboard] = useCopyToClipboard();
  const currentMemberInGroup = useConversationStore(
    (state) => state.currentMemberInGroup
  );

  useEffect(() => {
    if (isGroup) {
      IMSDK.getUsersInfo([creatorUserID || ownerUserID]).then((res) => {
        const { data } = res || {};
        setOwnerInfo(data[0]);
      });
    }
  }, [groupID, isGroup]);

  // const setCurrentMessageInputValue = useConversationStore(
  //   (state) => state.setCurrentMessageInputValue
  // );
  const memberQuit = async () => {
    if (groupID) {
      Modal.confirm({
        content: t('placeholder.exitGroupToast'),
        onOk: async () => {
          try {
            // if (
            //   conversationInfo?.conversationID != null &&
            //   conversationInfo?.draftText != null &&
            //   conversationInfo?.draftText !== ''
            // ) {
            //   setCurrentMessageInputValue(null);
            //   IMSDK.setConversationDraft({
            //     conversationID: conversationInfo.conversationID,
            //     draftText: '',
            //   });
            // }
            await IMSDK.quitGroup(groupID);
            handleCancel();
          } catch (error) {
            feedbackToast({ error, msg: '退出群会话失败，请确认你不是群主' });
          }
        },
      });
    } else {
    }
  };

  const introductionEditRole = useMemo(() => {
    return (
      currentMemberInGroup?.groupID &&
      currentMemberInGroup.roleLevel !== GroupMemberRole.Normal
    );
  }, [currentMemberInGroup]);

  return (
    <div className={styles.settingDetailsWrapper}>
      {isGroup && ownerUserID === selfUserID && (
        <div className={styles.moduleWrapper}>
          <div
            className={styles.paddingBox}
            onClick={() => {
              setModalVisible(true);
              setModalType('groupName');
            }}
          >
            <div className={styles.titleHeader}>
              <div>群聊名称</div>
              <div className={styles.editBtn}>编辑</div>
            </div>
            <div className={styles.desc}># {showName}</div>
          </div>
        </div>
      )}
      <div className={styles.moduleWrapper}>
        <div
          className={classNames(styles.paddingBox)}
          onClick={() => {
            if (notification || introductionEditRole) {
              setVisible(true);
            } else if (!introductionEditRole) {
              message.info('仅群主和管理员可发布群公告');
            }
          }}
        >
          <div className={styles.titleHeader}>
            <div>群公告</div>
            {introductionEditRole && <div className={styles.editBtn}>编辑</div>}
          </div>
          <div className={styles.desc}>
            {_.isEmpty(notification) ? (
              <div className={styles.emptyDesc}>添加群公告</div>
            ) : (
              <div className={styles.mdBox}>
                <RenderMd value={notification} id={`notification_${groupID}`} />
              </div>
            )}
          </div>
        </div>
      </div>
      {isGroup && (
        <div className={styles.moduleWrapper}>
          <div className={styles.paddingBox}>
            <div className={styles.titleHeader}>
              <div>创建者</div>
            </div>
            <div className={styles.desc}>
              {ownerInfo?.nickname} 位于{' '}
              {dayjs(createTime).format('YYYY年M月D日')}
            </div>
          </div>
          {ownerInfo?.userID !== selfUserID && (
            <div className={styles.paddingBox} onClick={memberQuit}>
              <div className={classNames(styles.titleHeader, styles.redHeader)}>
                <div>{t('placeholder.exitGroup')}</div>
              </div>
            </div>
          )}
        </div>
      )}
      <div className={styles.bottomWrapper}>
        群聊ID: {conversationID}
        <Tooltip title="复制群聊ID">
          <img
            src={copyIcon}
            style={{ width: '18px', cursor: 'pointer' }}
            onClick={() => {
              copyToClipboard(conversationID || '');
              feedbackToast({ msg: t('toast.copySuccess') });
            }}
          />
        </Tooltip>
      </div>

      {modalVisible && (
        <EditNameModal
          visible={modalVisible}
          handleCancel={() => setModalVisible(false)}
          groupInfo={groupInfo}
          modalType={modalType}
        />
      )}
      {visible && (
        <GroupAnnouncementModal
          visible={visible}
          isAdmin={introductionEditRole || false}
          groupInfo={groupInfo}
          onCancel={() => setVisible(false)}
        />
      )}
    </div>
  );
};

export default SettingDetails;
