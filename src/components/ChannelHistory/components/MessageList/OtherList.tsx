/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-lines */
/* eslint-disable max-statements */
import { useEffect, useState, useCallback, FC } from 'react';
import { useRequest } from 'ahooks';
import dayjs from 'dayjs';
import classNames from 'classnames';
import { Empty } from '@ht/sprite-ui';
import { throttle, isEmpty, reverse } from 'lodash';
import { Virtuoso } from '@ht/react-virtuoso';
import MessageItem from '@/components/Channel/components/MessageItem/index';
import {
  MessageItem as MessageItemType,
  MessageType,
} from '@ht/openim-wasm-client-sdk';
import emitter from '@/utils/events';
import { useConversationStore } from '@/store';
import { IMSDK } from '@/layouts/BasicLayout';
import { isSameDate } from '@/utils/date';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import searchEmpty from '@/assets/images/searchModal/searchEmpty.png';
import HistoryPictureItem from '../HistoryPictureItem';
import { ChannelHistoryHeaderTabType } from '../..';
import styles from './index.less';

const START_INDEX = 10000;
const SPLIT_COUNT = 50;
const Count = 80;

const CloudTypeList = ['110/clouddocument'];

interface OtherListProps {
  searchFilterParams: any;
  currentTab: ChannelHistoryHeaderTabType;
}

const OtherList: FC<OtherListProps> = ({ searchFilterParams, currentTab }) => {
  const { currentConversation } = useConversationStore();
  const conversationID = currentConversation?.conversationID || '';

  const [loadState, setLoadState] = useState({
    initLoading: true,
    hasMoreOld: true,
    messageList: [] as MessageItemType[],
    firstItemIndex: START_INDEX,
    initialTopMostItemIndex: SPLIT_COUNT - 1,
    messagePage: 0,
    loading: true,
  });

  const deleteOnewMessage = (clientMsgID: string) => {
    setLoadState((preState) => {
      const tmpList = [...preState.messageList];
      if (currentTab !== 'picture') {
        const idx = tmpList.findIndex((msg) => msg.clientMsgID === clientMsgID);
        if (idx < 0) {
          return preState;
        }
        tmpList.splice(idx, 1);

        return {
          ...preState,
          messageList: tmpList,
        };
      } else {
        const newList = tmpList.map((msg: any) => {
          const idx = msg.childList.findIndex(
            (listItem: MessageItemType) => clientMsgID === listItem.clientMsgID
          );
          if (idx < 0) {
            return msg;
          }
          msg.childList.splice(idx, 1);
          return msg;
        });
        return {
          ...preState,
          messageList: newList.filter((i) => !isEmpty(i.childList)),
        };
      }
    });
  };

  const updateOneMessage = (message: MessageItemType) => {
    if (message.contentType === MessageType.RevokeMessage) {
      deleteOnewMessage(message.clientMsgID);
    }
  };

  useEffect(() => {
    if (conversationID) {
      emitter.on('DELETE_ONE_MSG', deleteOnewMessage);
      emitter.on('UPDATE_ONE_MSG', updateOneMessage);
      getMoreOldMessages(false, 0);
    }
    return () => {
      emitter.off('DELETE_ONE_MSG', deleteOnewMessage);
      emitter.off('UPDATE_ONE_MSG', updateOneMessage);
    };
  }, [conversationID, searchFilterParams]);

  const { loading: moreOldLoading, runAsync: getMoreOldMessages } = useRequest(
    async (loadMore = true, page = 0) => {
      if (!loadMore) {
        setLoadState((pre) => {
          return {
            ...pre,
            initLoading: true,
            loading: true,
          };
        });
      } else {
        setLoadState((pre) => {
          return {
            ...pre,
            loading: true,
          };
        });
      }
      try {
        let isEnd = false;
        let resultItems: any = [];
        let customTypeList: any = [];
        let messageTypeList: any = [];
        if (currentTab === 'docs') {
          customTypeList = CloudTypeList;
          messageTypeList = [MessageType.CustomMessage];
        } else if (currentTab === 'file') {
          messageTypeList = [MessageType.FileMessage];
        } else if (currentTab === 'picture') {
          messageTypeList = [MessageType.PictureMessage];
        }
        const params = {
          conversationIDs: [conversationID],
          keywordList: isEmpty(searchFilterParams.searchValue)
            ? []
            : [searchFilterParams.searchValue],
          keywordListMatchType: 0,
          messageTypeList,
          searchTimePosition: searchFilterParams.searchTimePosition,
          searchTimePeriod: searchFilterParams.searchTimePeriod,
          customTypeList,
          pageIndex: page,
          count: Count,
          sort: [
            {
              field: 'send_time',
              ascending: false,
            },
          ],
        };
        const { data } = await IMSDK.searchServerMsg(params);

        resultItems = data.searchResultItems || [];
        isEnd = resultItems.length < Count;

        // 移除 setTimeout，直接更新状态
        setLoadState((preState) => {
          let resultDateList: any = [];
          if (currentTab === 'picture') {
            if (loadMore) {
              const preList: any = [...preState.messageList];
              resultItems?.forEach((item: any) => {
                if (
                  isSameDate(
                    new Date(preList[0].sendTime),
                    new Date(item.sendTime)
                  )
                ) {
                  preList[0].childList.unshift(item);
                } else {
                  const filterIndex = resultDateList.findIndex((i: any) =>
                    isSameDate(new Date(i.sendTime), new Date(item.sendTime))
                  );
                  if (filterIndex !== -1) {
                    resultDateList[filterIndex].childList.unshift(item);
                  } else {
                    resultDateList.unshift({
                      sendTime: item.sendTime,
                      childList: [item],
                      clientMsgID: item.clientMsgID, // 给一个id
                    });
                  }
                }
              });
              resultDateList = [...resultDateList, ...preList];
            } else {
              resultItems?.forEach((item: any) => {
                const filterIndex = resultDateList.findIndex((i: any) =>
                  isSameDate(new Date(i.sendTime), new Date(item.sendTime))
                );
                if (filterIndex !== -1) {
                  resultDateList[filterIndex].childList.unshift(item);
                } else {
                  resultDateList.unshift({
                    sendTime: item.sendTime,
                    childList: [item],
                    clientMsgID: item.clientMsgID, // 给一个id
                  });
                }
              });
            }
          } else {
            resultDateList = [
              ...reverse(resultItems),
              ...(loadMore ? preState.messageList : []),
            ];
          }

          return {
            ...preState,
            initLoading: false,
            hasMoreOld: !isEnd,
            messageList: resultDateList,
            firstItemIndex: preState.firstItemIndex - resultItems.length,
            initialTopMostItemIndex: loadMore
              ? SPLIT_COUNT - 1
              : resultItems.length - 1,
            messagePage: page,
            loading: false,
          };
        });
      } catch (e) {
        if (!loadMore) {
          setLoadState((preState) => ({
            ...preState,
            initLoading: false,
            hasMoreOld: false,
            firstItemIndex: 0,
            messagePage: 0,
            loading: false,
          }));
        }
      }
    },
    {
      manual: true,
    }
  );

  const loadMoreMessage = useCallback(
    throttle(
      () => {
        if (!loadState.hasMoreOld || moreOldLoading) {
          return;
        }
        getMoreOldMessages(true, loadState.messagePage + 1);
      },
      200,
      { leading: true, trailing: false }
    ),
    [loadState, moreOldLoading, searchFilterParams.searchValue]
  );

  const itemContentWrap = useCallback(
    (index: number, msg: MessageItemType) => {
      try {
        if (currentTab === 'picture') {
          return (
            <div className={styles.pictureMsgContent}>
              <div className={styles.sendDate}>
                {isSameDate(new Date(msg.sendTime), new Date())
                  ? '今天'
                  : dayjs(msg.sendTime).format('YYYY/MM/DD')}
              </div>
              <div className={styles.imgListWrapper}>
                {msg.childList.map((listItem: MessageItemType) => {
                  return (
                    <HistoryPictureItem
                      key={listItem.clientMsgID}
                      listItem={listItem}
                      conversationID={conversationID}
                      historyTab={currentTab}
                    />
                  );
                })}
              </div>
            </div>
          );
        } else {
          return (
            <div className={classNames(styles.listItemBorder)}>
              <MessageItem
                hasMoreMessageBefore={loadState.hasMoreOld}
                messageIndex={loadState.messageList?.findIndex(
                  (item) => item.clientMsgID === msg.clientMsgID
                )}
                clientMsgId={msg.clientMsgID}
                sendID={msg.sendID}
                key={msg.clientMsgID}
                message={msg}
                isSender={false}
                isThread={false}
                inRightThread={false}
                inHistoryList={true}
                isSearch={true}
                searchValue={searchFilterParams.searchValue}
                historyTab={currentTab}
                conversationID={conversationID}
              />
            </div>
          );
        }
      } catch (e) {
        console.error('渲染消息报错', e);
        return <></>;
      }
    },
    [loadState.hasMoreOld, loadState.messageList]
  );

  return (
    <div
      className={classNames(styles.messageListWrapper, styles.otherListWrapper)}
    >
      {loadState.initLoading || loadState.loading ? (
        <div
          style={{
            position: 'absolute',
            width: '100%',
            display: 'flex',
            justifyContent: 'center',
            padding: '5px 0',
          }}
        >
          <LoadingSpinner />
        </div>
      ) : (
        <Virtuoso
          style={{
            position: 'absolute',
            width: '100%',
          }}
          id={`${conversationID}_history_cloud`}
          className={styles.virtuosoListContainer}
          followOutput={(isAtBottom) => {
            if (document.hidden || !isAtBottom) {
              return false;
            }
            return 'smooth';
          }}
          firstItemIndex={loadState.firstItemIndex}
          initialTopMostItemIndex={{
            index: loadState.initialTopMostItemIndex,
            align: 'end',
          }}
          startReached={() => {
            if (loadState.initLoading || !loadState.hasMoreOld) {
              return;
            }
            loadMoreMessage();
          }}
          data={loadState.messageList}
          computeItemKey={(_, item) => item.clientMsgID}
          increaseViewportBy={{ top: 1500, bottom: 1200 }}
          defaultItemHeight={400}
          itemContent={itemContentWrap}
          components={{
            Header: () => {
              if (loadState.hasMoreOld && !loadState.initLoading) {
                return (
                  <div
                    style={{
                      position: 'fixed',
                      left: '50%',
                      display: 'flex',
                      justifyContent: 'center',
                      padding: '5px 0',
                      visibility: loadState.hasMoreOld ? 'visible' : 'hidden',
                    }}
                  >
                    <LoadingSpinner />
                  </div>
                );
              } else {
                return <></>;
              }
            },
            EmptyPlaceholder: () => {
              return (
                <Empty
                  image={<img src={searchEmpty} />}
                  description={<div>暂无相关聊天记录</div>}
                />
              );
            },
          }}
        />
      )}
    </div>
  );
};

export default OtherList;
