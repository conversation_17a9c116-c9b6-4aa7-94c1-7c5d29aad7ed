/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable indent */
/* eslint-disable max-lines */
/* eslint-disable max-statements */
import { FC, memo, useMemo, useState } from 'react';
import {
  MessageItem as MessageItemType,
  MessageType,
} from '@ht/openim-wasm-client-sdk';
import { v4 as uuidv4 } from 'uuid';
import { useConversationStore } from '@/store';
import copyIcon from '@/assets/channel/rightButtonMenu/copy.svg';
import forwardIcon from '@/assets/channel/rightButtonMenu/forward.svg';
import type { MenuProps } from '@ht/sprite-ui';
import { Menu } from '@ht/sprite-ui';
import { feedbackToast } from '@/utils/common';
import { IMSDK } from '@/layouts/BasicLayout';
import { deleteOneMessage } from '@/hooks/useHistoryMessageList';
import { selectedTextRef } from '@/hooks/useSelectedText';
import { parserMdToHtml } from '@/utils/parserMdToHtml';
import { mentionRegex } from '@/components/MdEditor/plugin-mention/MentionShecma';
import toSessionIcon from '@/assets/channelHistory/toSession.svg';
import ForwardModal from '@/components/ForwardModal';
import styles from './index.less';

const menus = ['toSession', 'copy', 'forwardMsg', 'divider', 'remove'];

interface RightButtonMenuProps {
  message: MessageItemType;
  conversationID: string;
  setRightBtnOpen: (val: boolean) => void;
  historyTab?: string;
}

const HistoryRightButtonMenu: FC<RightButtonMenuProps> = ({
  message,
  conversationID,
  setRightBtnOpen,
  historyTab = '',
}) => {
  const currentConversation = useConversationStore(
    (state) => state.currentConversation
  );
  const updateTargetMsg = useConversationStore(
    (state) => state.updateTargetMsg
  );
  const getCurrentMessageUpInfo = useConversationStore(
    (state) => state.getCurrentMessageUpInfo
  );
  const updateChannelHeaderCurTabAndConversation = useConversationStore(
    (state) => state.updateChannelHeaderCurTabAndConversation
  );
  const [forwardModal, setForwardModal] = useState<boolean>(false);

  const menuButtons: any = useMemo(() => {
    const tempMenu = {
      toSession: {
        label: (
          <div className={styles.rightButtonMenuItem}>
            <div>
              <img src={toSessionIcon} />
              <span>跳转至原文</span>
            </div>
          </div>
        ),
        key: 'toSession',
      },
      copy: {
        label: (
          <div className={styles.rightButtonMenuItem}>
            <div>
              <img src={copyIcon} />
              <span>复制</span>
            </div>
          </div>
        ),
        key: 'copy',
      },
      forwardMsg: {
        label: (
          <div className={styles.rightButtonMenuItem}>
            <div>
              <img src={forwardIcon} />
              <span>转发</span>
            </div>
          </div>
        ),
        key: 'forwardMsg',
      },
      divider: {
        type: 'divider',
      },
      remove: {
        label: (
          <div className={styles.rightButtonMenuItem}>
            <div>
              <span>删除</span>
            </div>
          </div>
        ),
        key: 'remove',
      },
    };
    return tempMenu;
  }, []);

  const getItems = useMemo(() => {
    let items: MenuProps['items'] = [];
    items = menus.map((key) => {
      if (
        message.contentType !== MessageType.TextMessage &&
        message.contentType !== MessageType.QuoteMessage &&
        message.contentType !== MessageType.MergeMessage &&
        message.contentType !== MessageType.AtTextMessage &&
        // eslint-disable-next-line eqeqeq
        key == 'copy'
      ) {
        return;
      }
      if (
        historyTab !== 'message' &&
        !['forwardMsg', 'toSession', 'divider', 'remove'].includes(key)
      ) {
        return;
      }
      return menuButtons[key];
    });
    return items;
  }, [menuButtons]);

  const itemClick = ({ item, key }: any) => {
    switch (key) {
      case 'toSession':
        handleViewTarget();
        break;
      case 'copy':
        handleCopy();
        break;
      case 'remove':
        tryRemove();
        break;
      case 'forwardMsg':
        setForwardModal(true);
        break;
      default:
        break;
    }
    setRightBtnOpen(false);
  };

  // 消息定位
  const handleViewTarget = async () => {
    const data = message;
    if (!data) {
      return;
    }
    if (data.contentType === MessageType.RevokeMessage) {
      return;
    }
    try {
      if (currentConversation?.conversationID != null) {
        updateChannelHeaderCurTabAndConversation(
          'message',
          {
            clientMsgID: data.clientMsgID,
            seq: data.seq,
          },
          {
            ...currentConversation,
            ex: `${uuidv4()}`,
          }
        );
      }
    } catch (error) {
      updateTargetMsg({
        clientMsgID: data.clientMsgID,
        seq: undefined,
      });
    }
  };

  const handleCopy = () => {
    const messageText = getMessageContent(message);
    const content =
      selectedTextRef.current ||
      parserMdToHtml(messageText.replace(mentionRegex, '@$1') || '');
    const clipboardData = new DataTransfer();
    clipboardData.setData('text/html', content);
    clipboardData.setData('text/plain', content.replace(/<[^>]+>/g, ''));

    try {
      navigator.clipboard.write([
        new ClipboardItem({
          'text/html': new Blob([content], { type: 'text/html' }),
          'text/plain': new Blob([content.replace(/<[^>]+>/g, '')], {
            type: 'text/plain',
          }),
        }),
      ]);
    } catch (error) {
      // 降级方案2：使用传统的 execCommand
      const tempElement = document.createElement('div');
      tempElement.innerHTML = content;
      tempElement.style.position = 'absolute';
      tempElement.style.left = '-9999px';
      document.body.appendChild(tempElement);

      // Create a range and select the content
      const range = document.createRange();
      range.selectNodeContents(tempElement);

      const selection = window.getSelection();
      selection?.removeAllRanges();
      selection?.addRange(range);

      // Execute the copy command
      const successful = document.execCommand('copy');
      if (!successful) {
        console.error('Failed to copy text using execCommand');
      }

      // Clean up
      selection?.removeAllRanges();
      document.body.removeChild(tempElement);
    }
    feedbackToast({ msg: '复制成功' });
  };

  const getMessageContent = (msg: MessageItemType): string => {
    switch (msg.contentType) {
      case MessageType.TextMessage:
        return msg.textElem?.content || '';
      case MessageType.QuoteMessage:
        return msg.quoteElem?.text || '';
      case MessageType.AtTextMessage:
        return msg.atTextElem?.text || '';
      case MessageType.MergeMessage:
        return msg.mergeElem?.title || '';
      default:
        return '';
    }
  };

  const tryRemove = async () => {
    try {
      await IMSDK.deleteMessage({
        clientMsgID: message.clientMsgID,
        conversationID,
      });
      deleteOneMessage(message.clientMsgID);
      if (message.groupID) {
        getCurrentMessageUpInfo(message.groupID);
      }
    } catch (error) {
      feedbackToast({ error, msg: '删除失败' });
    }
  };

  return (
    <>
      <Menu items={getItems} onClick={itemClick} />
      {forwardModal && (
        <ForwardModal
          open={forwardModal}
          onClose={() => setForwardModal(false)}
          message={message}
          isThread={false}
          isSender={false}
          conversationID={conversationID}
          showName={currentConversation?.showName}
        />
      )}
    </>
  );
};
export default memo(HistoryRightButtonMenu);
