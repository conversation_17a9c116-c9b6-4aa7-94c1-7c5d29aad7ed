import { FC, useState } from 'react';
import { Dropdown, message as Message } from '@ht/sprite-ui';
import ImgPreviewModal from '@/components/ImgPreviewModal';
import HistoryRightButtonMenu from '../HistoryRightButtonMenu';
import styles from './index.less';

interface HistoryPictureItemProps {
  listItem: any;
  conversationID: string;
  historyTab?: string;
}
const HistoryPictureItem: FC<HistoryPictureItemProps> = ({
  listItem,
  conversationID,
  historyTab = '',
}) => {
  const [rightBtnOpen, setRightBtnOpen] = useState(false);
  const [fileOpen, setFileOpen] = useState(false);

  const { pictureElem } = listItem;
  const { url = '', uuid } = pictureElem?.sourcePicture || {};
  const fileName = uuid?.split('/')[1] || 'image.png';

  const download = async () => {
    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('网络响应失败');
      }
      const blob = await response.blob();
      const blobUrl = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      URL.revokeObjectURL(blobUrl);
    } catch (error) {
      Message.error('下载失败');
    }
  };

  const openView = async () => {
    window.open(url, '_blank');
  };

  return (
    <div className={styles.historyPictureItemWrapper}>
      <Dropdown
        overlay={
          <HistoryRightButtonMenu
            message={listItem}
            conversationID={conversationID || ''}
            setRightBtnOpen={setRightBtnOpen}
            historyTab={historyTab}
          />
        }
        trigger={['contextMenu']}
        overlayClassName={styles.rightButtonMenu}
        open={rightBtnOpen}
        onOpenChange={(open: boolean) => {
          setRightBtnOpen(open);
        }}
      >
        <div className={styles.imgBox}>
          <img
            className={styles.showPicture}
            src={listItem.pictureElem?.sourcePicture.url}
            onClick={() => setFileOpen(true)}
          />
        </div>
      </Dropdown>

      {fileOpen && (
        <ImgPreviewModal
          open={fileOpen}
          onClose={() => setFileOpen(false)}
          download={download}
          imgMsg={listItem}
          fileName={fileName}
          showName={listItem.showName}
          openView={openView}
        />
      )}
    </div>
  );
};

export default HistoryPictureItem;
