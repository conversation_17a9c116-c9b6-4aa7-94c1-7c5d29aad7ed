import { Typography, Tooltip, message } from '@ht/sprite-ui';
import React, { useEffect, useMemo, useState } from 'react';
import OIMAvatar from '@/components/OIMAvatar';
import { EmployeeItem } from '@ht/openim-wasm-client-sdk';
import selectedIcon from '@/assets/images/forwardModal/selected.png';
import unselectedIcon from '@/assets/images/forwardModal/unselected.png';
import excludeIcon from '@/assets/images/forwardModal/excludeIcon.svg';
import useUserInfo from '@/hooks/useUserInfo';
import BotIconComponent from '@/components/ChannelList/BotIconComponent';
import classNames from 'classnames';
import styles from './index.less';
import { SeachedItemType } from '../..';

interface Props {
  employeeInfo: EmployeeItem;
  keyword: string;
  selectedList: SeachedItemType[];
  handleSelected: (conversation: SeachedItemType, isSelected: boolean) => void;
  position?: 'left' | 'right';
}

const ChannelUserItem: React.FC<Props> = ({
  employeeInfo,
  keyword,
  selectedList,
  handleSelected,
  position = 'left',
}) => {
  const [isSelected, setIsSelected] = useState(false);

  const { userDetail, userIsBot, multiSession } = useUserInfo(
    employeeInfo?.employeeID
  );
  const isMultiSession = multiSession && multiSession === 1;

  useEffect(() => {
    setIsSelected(
      selectedList?.some(
        (item) =>
          'employeeID' in item && item.employeeID === employeeInfo.employeeID
      )
    );
  }, [selectedList, employeeInfo.employeeID]);

  const handleHighLight = (keyWord = '', employeeCode = '') => {
    if (!keyWord) {
      return employeeCode;
    }

    const safeKeyWord = keyWord.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&'); // 转义特殊字符
    const regex = new RegExp(safeKeyWord, 'gi'); // 大小写不敏感，全局匹配

    return employeeCode.replace(
      regex,
      (match) => `<font style='color:#0074E2'>${match}</font>` // 保留原始大小写
    );
  };

  const defaultInfo = useMemo(() => {
    if (position === 'left' && keyword != null && keyword !== '') {
      return userDetail?.nickname || '';
    } else if (!userIsBot) {
      return `${userDetail?.nickname || ''}(${userDetail?.employeeCode || ''})`;
    } else {
      return userDetail?.nickname || '';
    }
  }, [
    keyword,
    position,
    userDetail?.employeeCode,
    userDetail?.nickname,
    userIsBot,
  ]);
  const renderExtraInfo = () => {
    if (position === 'left' && keyword != null && keyword !== '') {
      if (userDetail?.employeeCode.includes(keyword)) {
        return (
          <div className={styles.containerArea}>
            工号：
            <span
              // eslint-disable-next-line react/no-danger
              dangerouslySetInnerHTML={{
                __html: handleHighLight(keyword, userDetail?.employeeCode),
              }}
              className={styles.nickname}
            ></span>
          </div>
        );
      } else {
        // 机器人无职位信息
        const showText = `${
          userDetail?.positionInfos?.[0].departmentName || ''
        }${
          userDetail?.positionInfos?.[0]?.positionName
            ? `/${userDetail?.positionInfos?.[0]?.positionName}`
            : ''
        }`;

        return (
          <div className={styles.containerArea}>
            <Typography.Text
              ellipsis={true}
              className={styles.nickname}
              title={showText}
            >
              {showText}
            </Typography.Text>
          </div>
        );
      }
    } else {
      return <></>;
    }
  };
  return (
    <div
      className={classNames(
        styles.channelContainer,
        isSelected ? styles.selected : ''
      )}
      onClick={() => {
        if (position === 'right') {
          return;
        }
        if (isMultiSession) {
          message.info('不支持转发至智能体');
          return;
        }
        handleSelected(employeeInfo, !isSelected);
      }}
    >
      <div className={classNames(styles[position], styles.content)}>
        {position === 'left' && (
          <Tooltip title={isMultiSession ? '不支持转发至智能体' : null}>
            {isMultiSession ? (
              <div className={styles.botSelectIcon}></div>
            ) : (
              <img
                className={styles.selectedIcon}
                src={isSelected ? selectedIcon : unselectedIcon}
              ></img>
            )}
          </Tooltip>
        )}
        <div>
          <OIMAvatar
            userID={employeeInfo?.employeeID}
            size={38}
            hideOnlineStatus={true}
            stateSize={11}
            stateRight={-2}
          />
        </div>
        <div className={styles.channelInfo}>
          <div className={styles.channelName}>
            <>
              <div
                title={defaultInfo}
                className={styles.showName}
                // eslint-disable-next-line react/no-danger
                dangerouslySetInnerHTML={{
                  __html: handleHighLight(keyword, defaultInfo),
                }}
              ></div>
              {renderExtraInfo()}
            </>
          </div>
          <BotIconComponent userID={employeeInfo.employeeID} />
        </div>

        {position === 'right' && (
          <div
            className={styles.excludeArea}
            onClick={(e) => {
              e?.stopPropagation();
              handleSelected(employeeInfo, false);
              return false;
            }}
          >
            <img src={excludeIcon} className={styles.excludeIcon} />
          </div>
        )}
      </div>

      {position === 'left' && <div className={styles.line}></div>}
    </div>
  );
};
export default ChannelUserItem;
