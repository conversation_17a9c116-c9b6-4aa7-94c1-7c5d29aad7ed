.sider<PERSON>rapper {
  height: 100%;
  background: transparent;
  padding-top: 19px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;

  .menuItem {
    margin-bottom: 32px;
    font-size: 14px;
    font-weight: 700;
    color: rgba(0, 0, 0, 50%);
    line-height: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    position: relative;

    .state {
      background-color: rgba(255, 255, 255, 30%);
      border-radius: 8px 8px 0 0;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 36px;
      height: 32px;
      margin-top: -8px;
      margin-left: 0;
      padding: 4px 0 12px;
      font-size: 14px;
      line-height: 16px;
      animation: 0.3s user-status-icon;
      display: flex;
      position: relative;
      bottom: -8px;

      img {
        width: 16px;
        height: 16px;
      }

      @keyframes user-status-icon {
        0% {
          transform: translateY(24px);
        }
        100% {
          transform: translateY(0);
        }
      }
    }

    .icons,
    .selfbtn {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      margin-bottom: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      transition: background-color 0.3s ease;
      background-color: rgba(255, 255, 255, 19%);
      box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 18%);

      img {
        width: 20px;
      }
    }

    &:hover {
      .icons {
        transition: background-color 0.3s ease;
        background-color: rgba(255, 255, 255, 48%);
        box-shadow: 0 2px 8px 0 rgba(0, 46, 102, 20%);
      }
    }

    .unreadCount {
      position: absolute;
      top: -3px;
      right: -3px;
      width: 17px;
      height: 17px;
      border-radius: 50%;
      background: #d6363f;
      border: 1px solid #ffffff;
      color: #fff;
      font-size: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 400;
    }

    .unreadCountMore {
      width: 23px;
      border-radius: 9px;
      right: -9px;
    }
  }

  .active {
    color: #2f3035;
    .icons {
      background-color: rgba(255, 255, 255, 48%);
      box-shadow: 0 2px 8px 0 rgba(0, 46, 102, 20%);
    }
  }
}

.popStateContainer {
  :global {
    .linkflow-popover-content {
      border-radius: 5px;
    }
    .linkflow-popover-inner-content {
      padding: 8px 25px;
    }
    .linkflow-popover-arrow-content::before {
      background: black;
    }
  }
}

.hoverArea {
  display: flex;
  flex-direction: column;
  .name {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    color: #ffffff;
    font-size: 14px;
    text-align: center;
    font-weight: 500;
    line-height: 22px;
    height: 22px;
    > span {
      padding-right: 6px;
    }
  }
  .state {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    font-weight: 400;
    color: #808080;
    img {
      margin-right: 3px;
      width: 16px;
      height: 16px;
    }
  }
  .duration {
  }
}

.popPersonCardContainer {
  :global {
    .linkflow-popover-inner-content {
      padding: 0;
    }
    .linkflow-popover-content {
      border-radius: 10px;
    }
  }
}
