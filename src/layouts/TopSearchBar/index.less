.topBarContainer {
  width: 70%;
  height: 44px;
  display: flex;
  flex-shrink: 0;
  // justify-content: center;
  align-items: center;
  background: transparent;
  .searchInput {
    width: 100%;
    margin-left: calc((100vw - 72px) * 0.24 + 72px);
    background-color: rgba(255, 255, 255, 40%);
    border-radius: 5px;
    max-width: 1000px;
    min-width: 280px;
    z-index: 10;
    position: relative;
    input {
      border: none;
      background-color: transparent;
      padding: 3px 11px;
    }
    input::placeholder {
      color: rgba(102, 103, 113, 50%);
    }
    .searchIconBox {
      width: 14px;
      height: 14px;
      display: flex;
      align-items: center;
      position: absolute;
      top: 7px;
      right: 11px;
      > img {
        width: 14px;
        height: 14px;
      }
    }
  }
}
