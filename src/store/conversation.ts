/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-statements */
/* eslint-disable max-lines */

import {
  ConversationItem,
  GroupItem,
  GroupMemberItem,
  MessageItem,
} from '@ht/openim-wasm-client-sdk/lib/types/entity';
import { t } from 'i18next';
import { create } from 'zustand';
import _, { isEmpty } from 'lodash';
import { v4 as uuidv4 } from 'uuid';
import { getUserRoles, isBotUser, userRolesType } from '@/utils/avatar';
import { IMSDK } from '@/layouts/BasicLayout';
import { feedbackToast } from '@/utils/common';
import { conversationSort, isGroupSession } from '@/utils/imCommon';
import { devtools } from 'zustand/middleware';
import { GroupJoinSource } from '@ht/openim-wasm-client-sdk';
import { getDefaultThreadConversation } from '@/utils/utils';
import { ChannelHeaderTabType } from '@/components/Channel';
import {
  ConversationListUpdateType,
  ConversationStore,
  CHANGE_RIGHT_ARE_ACTION,
  LocalBotConfigDataType,
  CurrentBotConfigType,
  rightAreaInGroupListItem,
} from './type';
import { useUserStore } from './user';

const CONVERSATION_SPLIT_COUNT = 500;

export const useConversationStore = create<ConversationStore>()(
  devtools(
    (set, get) => ({
      conversationIniting: true,
      conversationList: [],
      currentConversation: undefined,
      unReadCount: 0,
      currentGroupInfo: undefined,
      currentMemberInGroup: undefined,
      currentMemberInGroupIniting: false,

      selectedUserID: '',
      currentSidebarMessage: undefined,
      currentSidebarData: undefined,
      currentSidebarActiveObj: undefined,
      rightAreaInGroupList: [], // 会话内右侧展示内容的历史列表
      rightAreaInGroupConversation: 'channelMemberList', // 会话框右侧展示的内容
      currentThreadConversation: undefined, // 修改时记得跟rightAreaInGroupConversation一起
      currentThreadGroupInfo: undefined,
      currentMemberInThreadGroup: undefined,
      currentThreadFirstMessage: undefined,
      // currentUserDetail: undefined, // 当前私聊会话对应的用户信息（非本人）---定时刷新
      llmLoading: false, // 流式消息是不是正在加载
      llmQuestions: {
        clientMsgId: '',
        questions: [],
      },

      targetMsg: {}, // 当前指定跳转的消息seq
      channelHeaderCurTab: 'message',
      isMultiSession: false,
      currentMultiSessionIniting: true,
      currentMultiSession: undefined,
      currentMultiSessionList: [],
      botConfig: [],
      botConfigModal: false,
      localBotConfigData: {},
      currentBotConfig: undefined,
      botQuestions: [],
      botIntro: '',

      currentReadSeqInfo: undefined,

      currentMessageUpInfo: [], // 置顶的消息

      clearReadSeqInfo: () => {
        set(() => ({
          currentReadSeqInfo: undefined,
        }));
      },
      removeSingleMsgFromUnReadList: (
        conversationID: string,
        msgSeq: number
      ) => {
        set((state) => {
          const { currentReadSeqInfo } = state;
          if (
            currentReadSeqInfo == null ||
            currentReadSeqInfo.conversationID !== conversationID
          ) {
            return {};
          }

          const { unReadSeqs, clicked } = currentReadSeqInfo;
          if (!unReadSeqs || unReadSeqs.length === 0 || clicked) {
            return {};
          }

          if (unReadSeqs.includes(msgSeq)) {
            const nextUnread = unReadSeqs.filter((item) => item !== msgSeq);
            return {
              currentReadSeqInfo: {
                ...currentReadSeqInfo,
                unReadSeqs: nextUnread,
                clicked: nextUnread.length === 0 ? true : clicked,
              },
            };
          } else if (msgSeq !== 0 && msgSeq < unReadSeqs[0]) {
            return {
              currentReadSeqInfo: {
                ...currentReadSeqInfo,
                unReadSeqs: [],
                clicked: true,
              },
            };
          }

          return {};
        });
      },
      updateReadSeqInfo: (
        conversationID: string,
        maxSeq: number,
        hasReadSeq: number,
        unReadSeqs: number[],
        clicked = false
      ) => {
        set(() => ({
          currentReadSeqInfo: {
            conversationID,
            // showBtn,
            maxSeq,
            hasReadSeq,
            unReadSeqs,
            clicked,
          },
        }));
      },

      hideReadSeqInfo: () => {
        const prevReadSeqInfo = get().currentReadSeqInfo;
        if (prevReadSeqInfo != null) {
          set(() => ({
            currentReadSeqInfo: {
              conversationID: prevReadSeqInfo.conversationID,
              // showBtn,
              maxSeq: prevReadSeqInfo.maxSeq,
              hasReadSeq: prevReadSeqInfo.hasReadSeq,
              unReadSeqs: prevReadSeqInfo.unReadSeqs,
              clicked: true,
            },
          }));
        }
      },

      // updateReadSeqInfoShowState: (showBtn: boolean) => {
      //   if (get()?.currentReadSeqInfo != null) {
      //     set(() => ({
      //       currentReadSeqInfo: {
      //         conversationID: get().currentReadSeqInfo?.conversationID,
      //         showBtn,
      //         maxSeq: get().currentReadSeqInfo?.maxSeq!,
      //         hasReadSeq: get().currentReadSeqInfo?.hasReadSeq!,
      //       },
      //     }));
      //   }
      // },

      updateTargetMsg: (msgInfo: { clientMsgID?: string; seq?: number }) => {
        set(() => ({ targetMsg: msgInfo }));
      },

      updateCurrentMemberInGroupIniting: (val: boolean) => {
        set(() => ({ currentMemberInGroupIniting: val }));
      },

      getConversationListByReq: async (
        isOffset?: boolean,
        forceLoading?: boolean
      ) => {
        if (forceLoading && !isOffset) {
          set(() => ({ conversationIniting: true }));
        }

        let tmpConversationList = [] as ConversationItem[];
        try {
          const { data } = await IMSDK.getConversationListSplit({
            offset: isOffset ? get().conversationList.length : 0,
            count: CONVERSATION_SPLIT_COUNT,
          });

          tmpConversationList = data;
        } catch (error) {
          feedbackToast({ error, msg: t('toast.getConversationFailed') });
          console.error('getConversationListByReq', error);
          if (!isOffset) {
            set(() => ({ conversationIniting: false }));
          }
          return true;
        }
        set((state) => ({
          conversationList: [
            ...(isOffset ? state.conversationList : []),
            ...tmpConversationList,
          ],
        }));
        if (forceLoading && !isOffset) {
          set(() => ({ conversationIniting: false }));
        }
        // 如果还有更多数据，递归调用自身
        if (
          forceLoading &&
          tmpConversationList.length === CONVERSATION_SPLIT_COUNT
        ) {
          await get().getConversationListByReq(true, true); // 继续加载下一页
        }

        return tmpConversationList.length === CONVERSATION_SPLIT_COUNT;
      },
      updateConversationList: (
        list: ConversationItem[],
        type: ConversationListUpdateType
      ) => {
        const {
          currentConversation,
          currentThreadConversation,
          conversationList,
          currentMultiSession,
          currentMultiSessionList,
        } = get();
        const chids = new Set<string>();
        const multiSessionChids = new Set<string>();
        const newConversationList: ConversationItem[] = [];
        const newMultiSessionList: ConversationItem[] = [];
        let updatedCurrentConversation = currentConversation;
        let updatedThreadConversation = currentThreadConversation;
        let updatedMultiSession = currentMultiSession;

        list.forEach((conversation) => {
          if (
            conversation.parentId &&
            conversation.parentId === currentConversation?.conversationID
          ) {
            newMultiSessionList.push(conversation);
            multiSessionChids.add(conversation.conversationID);
            if (
              conversation.conversationID ===
              updatedMultiSession?.conversationID
            ) {
              updatedMultiSession = {
                ...updatedMultiSession,
                ...conversation,
              };
            }
          } else {
            newConversationList.push(conversation);
            chids.add(conversation.conversationID);
            if (
              conversation.conversationID ===
              currentConversation?.conversationID
            ) {
              updatedCurrentConversation = conversation;

              if (currentConversation.ex) {
                updatedCurrentConversation.ex = currentConversation.ex;
              }
            }
            if (
              conversation.conversationID ===
              currentThreadConversation?.conversationID
            ) {
              updatedThreadConversation = {
                ...currentThreadConversation,
                ...conversation,
              };
            }
          }
        });

        // 更新会话列表
        const filterArr =
          type === 'filter'
            ? conversationList.filter((tc) => !chids.has(tc.conversationID))
            : conversationList;

        const isDifferentConversation = !_.isEqual(
          updatedCurrentConversation,
          currentConversation
        );

        console.debug('更新会话', isDifferentConversation, newConversationList);

        // 更新子会话会话列表
        let multiSessionFilterArr = currentMultiSessionList;
        if (type === 'filter') {
          multiSessionFilterArr = currentMultiSessionList.filter(
            (tc) => !multiSessionChids.has(tc.conversationID)
          );
        }

        if (isDifferentConversation) {
          get().updateCurrentConversation(updatedCurrentConversation);
        }
        if (!_.isEqual(updatedMultiSession, currentMultiSession)) {
          get().updateCurrentMultiSession(updatedMultiSession);
        }

        set(() => ({
          conversationList: conversationSort([
            ...newConversationList,
            ...filterArr,
          ]),
          currentMultiSessionList: conversationSort([
            ...newMultiSessionList,
            ...multiSessionFilterArr,
          ]),
        }));
      },
      delConversationByCID: (conversationID: string) => {
        const { conversationList } = get();

        const updatedConversationList = conversationList.filter(
          (cve) => cve.conversationID !== conversationID
        );

        set(() => ({
          conversationList: updatedConversationList,
        }));
      },

      // 前端清除会话数
      clearConversationUnReadCount: (conversation: ConversationItem) => {
        if (conversation === null) {
          return;
        }
        const { conversationList } = get();

        const updatedList = conversationList?.map((item) => {
          if (item.conversationID === conversation?.conversationID) {
            return {
              ...item,
              unreadCount: 0, // 清除未读数
            };
          }
          return item;
        });

        set({ conversationList: updatedList });
      },
      // 左侧会话列表的时候调用，thread之间的切换不能用这个
      updateCurrentConversation: async (
        conversation?: ConversationItem,
        _isJump?: boolean
      ) => {
        if (!conversation) {
          set(() => ({
            currentConversation: undefined,
            quoteMessage: undefined,
            currentGroupInfo: undefined,
            currentMemberInGroup: undefined,
            currentThreadGroupInfo: undefined,
            currentThreadMemberInGroup: undefined,
          }));
          return;
        }
        const prevConversation = get().currentConversation;
        const toggleNewConversation =
          conversation.conversationID !== prevConversation?.conversationID;

        if (prevConversation != null && toggleNewConversation) {
          // 切换会话时，立刻清除上一会话和本会话的未读数
          get().clearConversationUnReadCount(prevConversation);
          get().clearConversationUnReadCount(conversation);

          get().clearReadSeqInfo();
        }

        const multiSessionFlag = await get().checkIsMultiSession(conversation);
        if (toggleNewConversation) {
          // 切换左侧会话列表时，要把右侧thread框状态清空
          if (multiSessionFlag) {
            get().changeRightArea(
              'OPEN_ROBOT_CONVERSATION_AREA',
              conversation.conversationID
            );
          } else {
            get().changeRightArea('CLEAR_RIGHT_AREA');
          }

          // 如果切到群会话，获取群信息和群成员信息
          if (
            isGroupSession(conversation.conversationType) &&
            conversation.groupID != null &&
            conversation.groupID !== ''
          ) {
            get().getCurrentGroupInfoByReq(conversation.groupID);
            get().getCurrentMemberInGroupByReq(conversation.groupID);
            get().getCurrentMessageUpInfo(conversation.groupID);
          }
          // // 私聊的话则查用户信息
          // else {
          //   get().getCurrentUserDetail(conversation.userID);
          // }
          //
        }

        // const prevMultiSession = get().currentMultiSession;
        if (multiSessionFlag) {
          set(() => ({
            currentConversation: conversation,
            isMultiSession: multiSessionFlag,
          }));
        } else {
          set(() => ({
            currentConversation: conversation,
            isMultiSession: multiSessionFlag,
            currentMultiSession: undefined,
          }));
        }
      },

      // 更新会话内容
      updateConversation: (newConversationItem?: ConversationItem) => {
        const { conversationList } = get();

        if (newConversationItem == null) {
          return;
        }
        // 更新会话列表
        const newConversationList = conversationList.map((converationItem) => {
          if (
            converationItem.conversationID ===
            newConversationItem.conversationID
          ) {
            return {
              ...newConversationItem,
            };
          } else {
            return converationItem;
          }
        });

        set(() => ({
          conversationList: [...newConversationList],
        }));
      },

      // 打开右侧待创建的thread
      openThreadConversation: async (
        parentId: string,
        message: MessageItem
      ) => {
        set(() => ({
          rightAreaInGroupConversation: 'thread',
          currentThreadConversation: undefined,
          selectedUserID: '',
          currentThreadFirstMessage: message,
          currentThreadGroupInfo: {
            parentId,
          } as GroupItem, // 塞一下默认值
          currentMemberInThreadGroup: {
            groupID: '',
            userID: useUserStore.getState().selfInfo.userID,
            nickname: useUserStore.getState().selfInfo.nickname,
            faceURL: useUserStore.getState().selfInfo.faceURL,
            roleLevel: useUserStore.getState().selfInfo.level,
            muteEndTime: new Date().getTime(),
            joinTime: new Date().getTime(),
            joinSource: GroupJoinSource.Invitation,
            inviterUserID: '',
            operatorUserID: '',
            ex: '',
          }, // 塞一下默认值
        }));
      },

      // 设置当前thread会话
      setCurrentThreadConversation: async (
        threadID: string,
        parentID: string,
        message: MessageItem
      ) => {
        try {
          set(() => ({
            rightAreaInGroupConversation: 'thread',
            selectedUserID: '',
            currentThreadConversation:
              get().conversationList?.filter(
                (conversation) => conversation.groupID === threadID
              )?.[0] ?? getDefaultThreadConversation(threadID),
            currentThreadFirstMessage: message,
            currentThreadGroupInfo: undefined, // 这里是不是要把当前用户塞进去？
            currentMemberInThreadGroup: undefined, // 这里是不是要把当前用户塞进去？
          }));
          get().getCurrentThreadGroupInfoByReq(threadID, parentID);
          get().getCurrentMemberInThreadGroupByReq(threadID, parentID);
        } catch (e) {
          console.error('查询当前thread初始化信息失败');
        }
      },

      // thread之间的切换
      updateCurrentThreadConversation: async (
        conversation?: ConversationItem,
        _isJump?: boolean
      ) => {
        try {
          if (!conversation) {
            set(() => ({
              currentConversation: undefined,
              quoteMessage: undefined,
              currentGroupInfo: undefined,
              currentMemberInGroup: undefined,
              currentThreadGroupInfo: undefined,
              currentThreadMemberInGroup: undefined,
            }));
            return;
          }
          const prevConversation = get().currentThreadConversation;

          const toggleNewConversation =
            conversation.conversationID !== prevConversation?.conversationID;

          if (
            toggleNewConversation &&
            isGroupSession(conversation.conversationType)
          ) {
            if (get().currentThreadGroupInfo?.parentID == null) {
              console.error('更新currentThreadConversation失败，parentID为空');
              return;
            }
            get().getCurrentThreadGroupInfoByReq(
              conversation.groupID,
              get().currentThreadGroupInfo?.parentID ?? ''
            );
            get().getCurrentMemberInThreadGroupByReq(
              conversation.groupID,
              get().currentThreadGroupInfo?.parentID ?? ''
            );
          }

          // if (prevConversation?.parentId) {
          //   set(() => ({
          //     currentThreadConversation: {
          //       ...conversation,
          //       parentId: prevConversation?.parentId || '', // 不能修改parentId
          //     },
          //   }));
          // } else {
          set(() => ({
            currentThreadConversation: {
              ...conversation,
            },
          }));
          // }
        } catch (e) {
          console.error('updateCurrentThreadConversation失败，原因是：', e);
        }
      },
      updateCurrentConversationFields: (fields: Partial<ConversationItem>) => {
        set((state) => ({
          currentConversation: {
            ...state.currentConversation!,
            ...fields,
          },
        }));
      },

      // 清空当前在群聊天框右侧打开的thread
      clearCurrentThreadConversation: () => {
        //
        set(() => ({
          // rightAreaInGroupConversation: undefined,
          currentThreadConversation: undefined,
          currentThreadGroupInfo: undefined,
          currentMemberInThreadGroup: undefined,
          currentThreadFirstMessage: undefined,
          // currentUserDetail: undefined,
        }));
      },

      getUnReadCountByReq: async () => {
        try {
          const { data } = await IMSDK.getTotalUnreadMsgCount();
          set(() => ({ unReadCount: data }));
          return data;
        } catch (error) {
          console.error(error);
          return 0;
        }
      },
      updateUnReadCount: (count: number) => {
        set(() => ({ unReadCount: count }));
      },

      getCurrentGroupInfoByReq: async (groupID: string) => {
        let groupInfo: GroupItem;
        try {
          const { data } = await IMSDK.getSpecifiedGroupsInfo([groupID]);
          groupInfo = data[0];
        } catch (error) {
          // feedbackToast({ error, msg: t('toast.getGroupInfoFailed') });
          return;
        }
        set(() => ({ currentGroupInfo: { ...groupInfo } }));
      },
      getCurrentThreadGroupInfoByReq: async (
        groupID: string,
        parentId: string
      ) => {
        let groupInfo: GroupItem;
        try {
          const { data } = await IMSDK.getSpecifiedGroupsInfo([groupID]);
          groupInfo = data[0];
        } catch (error) {
          // feedbackToast({ error, msg: t('toast.getGroupInfoFailed') });
          return;
        }
        set(() => ({ currentThreadGroupInfo: { ...groupInfo, parentId } }));
      },

      getCurrentMessageUpInfo: async (groupID?: string) => {
        if (
          groupID === get().currentConversation?.groupID &&
          get().currentConversation?.groupID
        ) {
          const currentId = get().currentConversation?.groupID || '';
          // 获取置顶消息
          try {
            const { data } = await IMSDK.getGroupUpMessages(currentId);
            set(() => ({ currentMessageUpInfo: data }));
          } catch (error) {}
        }
      },

      updateCurrentGroupInfo: (groupInfo: GroupItem) => {
        set(() => ({ currentGroupInfo: { ...groupInfo } }));
      },
      updateCurrentThreadGroupInfo: (
        groupInfo: GroupItem,
        parentId: string
      ) => {
        set(() => ({ currentThreadGroupInfo: { ...groupInfo, parentId } }));
      },

      getCurrentMemberInGroupByReq: async (groupID: string) => {
        let memberInfo: GroupMemberItem;
        let selfID = useUserStore.getState().selfInfo.userID;
        if (!selfID) {
          selfID = (await IMSDK.getSelfUserInfo()).data.userID;
        }
        try {
          const { data } = await IMSDK.getSpecifiedGroupMembersInfo({
            groupID,
            userIDList: [selfID],
          });
          memberInfo = data[0];
        } catch (error) {
          set(() => ({ currentMemberInGroup: undefined }));
          get().updateCurrentMemberInGroupIniting(false);
          // feedbackToast({ error, msg: t('toast.getGroupMemberFailed') });
          return;
        }
        set(() => ({
          currentMemberInGroup: memberInfo ? { ...memberInfo } : undefined,
        }));
        get().updateCurrentMemberInGroupIniting(false);
      },
      getCurrentMemberInThreadGroupByReq: async (
        groupID: string,
        parentId: string
      ) => {
        let memberInfo: GroupMemberItem;
        const selfID = useUserStore.getState().selfInfo.userID;
        try {
          const { data } = await IMSDK.getSpecifiedGroupMembersInfo({
            groupID,
            userIDList: [selfID],
          });
          memberInfo = data[0];
        } catch (error) {
          console.error(`查询子群${groupID}当前成员信息失败，原因为`, error);
          set(() => ({ currentMemberInThreadGroup: undefined }));
          // feedbackToast({ error, msg: t('toast.getGroupMemberFailed') });
          return;
        }

        set(() => ({
          currentMemberInThreadGroup: memberInfo,
        }));
      },
      setCurrentMemberInGroup: (memberInfo?: GroupMemberItem) => {
        set(() => ({ currentMemberInGroup: memberInfo }));
      },
      setCurrentMemberInThreadGroup: (memberInfo?: GroupMemberItem) => {
        set(() => ({ currentMemberInThreadGroup: memberInfo }));
      },

      tryUpdateCurrentMemberInGroup: (member: GroupMemberItem) => {
        const { currentMemberInGroup } = get();
        if (
          member.groupID === currentMemberInGroup?.groupID &&
          member.userID === currentMemberInGroup?.userID
        ) {
          set(() => ({ currentMemberInGroup: { ...member } }));
        }
      },
      tryUpdateCurrentMemberInThreadGroup: (member: GroupMemberItem) => {
        const { currentMemberInThreadGroup } = get();
        if (
          member.groupID === currentMemberInThreadGroup?.groupID &&
          member.userID === currentMemberInThreadGroup?.userID
        ) {
          set(() => ({ currentMemberInThreadGroup: { ...member } }));
        }
      },

      changeRightArea: async (
        changeRightAction: CHANGE_RIGHT_ARE_ACTION,
        payload
      ) => {
        if (changeRightAction === 'OPEN_THREAD') {
          set((state) => {
            let newRightAreaInGroupList = [...state.rightAreaInGroupList];
            newRightAreaInGroupList = newRightAreaInGroupList.filter(
              (i: rightAreaInGroupListItem) => i.type !== 'thread'
            );
            newRightAreaInGroupList.push({
              type: 'thread',
              key: uuidv4(),
            });
            return {
              rightAreaInGroupConversation: 'thread',
              selectedUserID: '',
              currentSidebarMessage: undefined,
              currentSidebarData: undefined,
              rightAreaInGroupList: newRightAreaInGroupList,
            };
          });
          get().setCurrentThreadConversation(
            ...(payload as [string, string, MessageItem])
          );
        } else if (changeRightAction === 'OPEN_PERSON_DETAIL') {
          get().clearCurrentThreadConversation();
          set((state) => {
            let newRightAreaInGroupList = [...state.rightAreaInGroupList];
            newRightAreaInGroupList = newRightAreaInGroupList.filter(
              (i: rightAreaInGroupListItem) => i.type !== 'personDetail'
            );
            newRightAreaInGroupList.push({
              type: 'personDetail',
              payload,
              key: uuidv4(),
            });
            return {
              rightAreaInGroupConversation: 'personDetail',
              selectedUserID: payload,
              currentSidebarMessage: undefined,
              currentSidebarData: undefined,
              rightAreaInGroupList: newRightAreaInGroupList,
            };
          });
        } else if (changeRightAction === 'OPEN_FILE_AREA') {
          get().clearCurrentThreadConversation();
          set((state) => {
            let newRightAreaInGroupList = [...state.rightAreaInGroupList];
            newRightAreaInGroupList = newRightAreaInGroupList.filter(
              (i: rightAreaInGroupListItem) => i.type !== 'file'
            );
            newRightAreaInGroupList.push({
              type: 'file',
              payload,
              key: uuidv4(),
            });
            return {
              rightAreaInGroupConversation: 'file',
              currentSidebarMessage: payload,
              currentSidebarData: undefined,
              selectedUserID: '',
              rightAreaInGroupList: newRightAreaInGroupList,
            };
          });
        } else if (changeRightAction === 'OPEN_ROBOT_CONVERSATION_AREA') {
          get().clearCurrentThreadConversation();
          get().getCurrentMultiSessionList(payload);
          set((state) => {
            let newRightAreaInGroupList = [...state.rightAreaInGroupList];
            newRightAreaInGroupList = newRightAreaInGroupList.filter(
              (i: rightAreaInGroupListItem) => i.type !== 'robotConversation'
            );
            newRightAreaInGroupList.push({
              type: 'robotConversation',
              payload,
              key: uuidv4(),
            });
            return {
              rightAreaInGroupConversation: 'robotConversation',
              currentSidebarMessage: undefined,
              currentSidebarData: undefined,
              selectedUserID: '',
              rightAreaInGroupList: newRightAreaInGroupList,
            };
          });
        } else if (
          changeRightAction === 'OPEN_ROBOT_ANSWER_SOURCE' ||
          changeRightAction === 'OPEN_ROBOT_ONLINE_SEARCE'
        ) {
          const currentChangeRightAction =
            changeRightAction === 'OPEN_ROBOT_ANSWER_SOURCE'
              ? 'robotAnswerSource'
              : 'robotOnlineSearch';
          get().clearCurrentThreadConversation();
          set((state) => {
            let newRightAreaInGroupList = [...state.rightAreaInGroupList];
            newRightAreaInGroupList = newRightAreaInGroupList.filter(
              (i: rightAreaInGroupListItem) =>
                i.type !== currentChangeRightAction
            );
            newRightAreaInGroupList.push({
              type: currentChangeRightAction,
              payload,
              key: uuidv4(),
            });
            return {
              rightAreaInGroupConversation: currentChangeRightAction,
              currentSidebarMessage: undefined,
              currentSidebarData: payload?.data || undefined,
              currentSidebarActiveObj: payload?.activeObj || undefined,
              selectedUserID: '',
              rightAreaInGroupList: newRightAreaInGroupList,
            };
          });
        } else if (changeRightAction === 'OPEN_CHANNEL_HISTORY') {
          get().clearCurrentThreadConversation();
          set((state) => {
            let newRightAreaInGroupList = [...state.rightAreaInGroupList];
            newRightAreaInGroupList = newRightAreaInGroupList.filter(
              (i: rightAreaInGroupListItem) => i.type !== 'channelHistory'
            );
            newRightAreaInGroupList.push({
              type: 'channelHistory',
              payload,
              key: uuidv4(),
            });
            return {
              rightAreaInGroupConversation: 'channelHistory',
              currentSidebarMessage: undefined,
              currentSidebarData: undefined,
              selectedUserID: '',
              rightAreaInGroupList: newRightAreaInGroupList,
            };
          });
        } else {
          set(() => ({
            rightAreaInGroupConversation: 'channelMemberList',
            selectedUserID: undefined,
            currentSidebarMessage: undefined,
            currentSidebarData: undefined,
            currentSidebarActiveObj: undefined,
            rightAreaInGroupList: [],
          }));
          get().clearCurrentThreadConversation();
        }
      },
      checkRightArea: (message: MessageItem) => {
        const key = get().rightAreaInGroupConversation;
        if (key === 'file') {
          const sidebarMessage = get().currentSidebarMessage;
          if (sidebarMessage?.clientMsgID === message.clientMsgID) {
            set(() => ({
              rightAreaInGroupConversation: 'channelMemberList',
              selectedUserID: undefined,
              currentSidebarMessage: undefined,
              currentSidebarData: undefined,
              currentSidebarActiveObj: undefined,
              rightAreaInGroupList: [],
            }));
            get().clearCurrentThreadConversation();
          }
        }
      },
      changeRightAreaBack: async () => {
        set((state) => {
          if (state.rightAreaInGroupList.length > 1) {
            const newRightAreaInGroupList = state.rightAreaInGroupList;
            newRightAreaInGroupList.pop();
            const lastRightAreaInfo =
              newRightAreaInGroupList[newRightAreaInGroupList.length - 1];
            return {
              rightAreaInGroupConversation: lastRightAreaInfo.type,
              selectedUserID:
                lastRightAreaInfo.type === 'personDetail'
                  ? lastRightAreaInfo.payload
                  : '',
              currentSidebarMessage:
                lastRightAreaInfo.type === 'file'
                  ? lastRightAreaInfo.payload
                  : undefined,
              rightAreaInGroupList: newRightAreaInGroupList,
              currentSidebarData: lastRightAreaInfo.payload?.data || undefined,
              currentSidebarActiveObj:
                lastRightAreaInfo.payload?.activeObj || undefined,
            };
          } else {
            return {
              rightAreaInGroupConversation: 'channelMemberList',
              selectedUserID: undefined,
              currentSidebarMessage: undefined,
              currentSidebarData: undefined,
              rightAreaInGroupList: [],
            };
          }
        });
      },
      clearConversationStore: () => {
        set(() => ({
          conversationList: [],
          currentConversation: undefined,
          unReadCount: 0,
          currentGroupInfo: undefined,
          currentMemberInGroup: undefined,
          rightAreaInGroupConversation: 'channelMemberList',
          currentThreadConversation: undefined,
          currentThreadGroupInfo: undefined,
          currentMemberInThreadGroup: undefined,
          currentThreadFirstMessage: undefined,
          quoteMessage: undefined,
          llmLoading: false,
        }));
      },
      updateChannelHeaderCurTab: (val: ChannelHeaderTabType) => {
        set(() => ({
          channelHeaderCurTab: val,
        }));
      },
      updateChannelHeaderCurTabAndConversation: (
        val: ChannelHeaderTabType,
        msgInfo: { clientMsgID?: string; seq?: number },
        conversation: ConversationItem
      ) => {
        set(() => ({
          currentConversation: conversation,
          targetMsg: msgInfo,
          channelHeaderCurTab: val,
        }));
      },
      updateLlmLoading: (value: boolean) => {
        set(() => ({
          llmLoading: value,
        }));
      },

      // currentMessageInputFunc: undefined,
      // setGetCurrentMessageInputFunc: (prop: () => string) => {
      //   set(() => ({ currentMessageInputFunc: prop }));
      // },

      currentMessageInputValue: undefined,
      setCurrentMessageInputValue: (value) => {
        set((state) => ({
          currentMessageInputValue: value,
        }));
      },
      getCurrentMessageInputValue: () => {
        return get().currentMessageInputValue;
      },
      updateLlmQuestions: (questions: string[], clientMsgID: string) => {
        set(() => ({
          llmQuestions: {
            questions,
            clientMsgId: clientMsgID,
          },
        }));
      },
      checkIsMultiSession: async (conversation: ConversationItem) => {
        let multiSessionFlag = false;
        if (!conversation?.groupID) {
          const isBot = isBotUser(conversation?.userID);
          if (isBot) {
            try {
              const userDetailDataResponse = await IMSDK.getUsersInfo([
                conversation?.userID,
              ]);
              const userEx = JSON.parse(
                userDetailDataResponse.data[0]?.ex || '{}'
              );
              const { multiSession, inputs, questions, intro } = userEx || {};
              if (multiSession && multiSession === 1) {
                get().initCurrentMultiSession(conversation);
                multiSessionFlag = true;
              }
              set(() => ({
                botConfig: inputs || [],
                botQuestions: questions || [],
                botIntro: intro || '',
              }));
            } catch (error) {}
          }
        }
        return multiSessionFlag;
      },
      getCurrentMultiSessionList: async (conversationID: string) => {
        try {
          set(() => ({
            currentMultiSessionIniting: true,
          }));
          const { data } = await IMSDK.getAllSubConversationList(
            conversationID
          );
          set(() => ({
            currentMultiSessionList: data || [],
            currentMultiSessionIniting: false,
          }));
        } catch (error) {
          set(() => ({
            currentMultiSessionList: [],
            currentMultiSessionIniting: false,
          }));
          console.error('getCurrentMultiSessionList', error);
        }
      },
      updateCurrentMultiSession: (
        conversation?: ConversationItem,
        isJump?: boolean
      ) => {
        if (!conversation) {
          set(() => ({
            currentMultiSession: undefined,
            currentBotConfig: undefined,
            // currentMultiSessionList: [],
          }));
        } else {
          const prevConversation = get().currentMultiSession;
          const toggleNewConversation =
            conversation.conversationID !== prevConversation?.conversationID;
          const nowConversation = get().currentConversation;
          if (!isJump && toggleNewConversation) {
            if (
              conversation.subConversationId != null &&
              conversation.subConversationId !== ''
            ) {
              get().getCurrentMultiSessionList(
                nowConversation?.conversationID || ''
              );
            }
          }
          const key = conversation?.conversationID;
          const localData = get().localBotConfigData;
          const nextCurrentBotConfig = localData[key] || undefined;
          set(() => ({
            currentMultiSession: conversation,
            currentBotConfig: nextCurrentBotConfig || undefined,
          }));
        }
      },
      updateRobotConfig: async (val: CurrentBotConfigType) => {
        const localData = get().localBotConfigData;
        const multiSessionConversation = get().currentMultiSession;
        if (multiSessionConversation) {
          const key = multiSessionConversation?.conversationID;
          localData[key] = val;
          let selfID = useUserStore.getState().selfInfo.userID;
          if (!selfID) {
            selfID = (await IMSDK.getSelfUserInfo()).data.userID;
          }
          const localKey = `${selfID}_localBotConfigData`;
          const localBotStr = JSON.stringify(localData, (key, value) =>
            value === undefined ? 'undefined' : value
          );
          localStorage.setItem(localKey, localBotStr);
        }
        set(() => ({
          currentBotConfig: val,
          localBotConfigData: localData,
        }));
      },
      createCurrentMultiSession: async () => {
        const conversation = get().currentConversation;
        const multiSession = get().currentMultiSession;
        // 判断当前会话有没有发送消息，如果未发送消息，不需要新建会话
        if (!multiSession?.latestMsg) {
          return;
        }
        const sourceID = conversation?.userID || conversation?.groupID || '';
        const { data } = await IMSDK.getNewSubConversation({
          sourceID,
          sessionType: 1,
        });
        get().updateCurrentMultiSession(data, true);
      },
      updateBotConfigModal: (
        open: boolean,
        type: 'default' | 'reset' = 'default'
      ) => {
        if (type === 'default') {
          set(() => ({
            botConfigModal: open,
          }));
        } else {
          const nowCurrentBotConfig = get().currentBotConfig;
          if (nowCurrentBotConfig) {
            set(() => ({
              botConfigModal: open,
              currentBotConfig: {
                ...nowCurrentBotConfig,
                data: {},
              },
            }));
          }
        }
      },
      initCurrentMultiSession: async (conversation: ConversationItem) => {
        try {
          const sourceID = conversation?.userID || conversation?.groupID || '';
          const { data } = await IMSDK.getLatestConversation({
            sourceID,
            sessionType: 1,
          });
          let localBotConfig: LocalBotConfigDataType = {};
          let localCurrentBotConfig: CurrentBotConfigType | undefined;
          let selfID = useUserStore.getState().selfInfo.userID;
          if (!selfID) {
            selfID = (await IMSDK.getSelfUserInfo()).data.userID;
          }
          const localKey = `${selfID}_localBotConfigData`;
          const localBotData = localStorage.getItem(localKey);
          // eslint-disable-next-line max-depth
          if (localBotData) {
            localBotConfig = JSON.parse(localBotData, (key, value) =>
              value === 'undefined' ? null : value
            );
            const key = data?.conversationID;
            localCurrentBotConfig = localBotConfig?.[key]
              ? localBotConfig[key]
              : undefined;
          }
          set(() => ({
            currentMultiSession: data,
            currentBotConfig: localCurrentBotConfig,
            localBotConfigData: localBotConfig || {},
          }));
        } catch (error) {
          set(() => ({
            currentMultiSession: undefined,
            currentBotConfig: undefined,
            localBotConfigData: {},
          }));
        }
      },
      updateMultiSessionForSearch: async (conversation: ConversationItem) => {
        const { conversationID } = conversation;
        const id = conversationID.split('@')[0] || '';
        const userDetailDataResponse = await IMSDK.getUsersInfo([
          conversation?.userID,
        ]);
        const userEx = JSON.parse(userDetailDataResponse.data[0]?.ex || '{}');
        const { inputs, questions, intro } = userEx || {};
        const key = conversationID;
        let localBotConfig = get().localBotConfigData;
        console.warn('updateMultiSessionForSearch', localBotConfig);
        if (isEmpty(localBotConfig)) {
          let selfID = useUserStore.getState().selfInfo.userID;
          if (!selfID) {
            selfID = (await IMSDK.getSelfUserInfo()).data.userID;
          }
          const localKey = `${selfID}_localBotConfigData`;
          const localBotData = localStorage.getItem(localKey);
          // eslint-disable-next-line max-depth
          if (localBotData) {
            // eslint-disable-next-line require-atomic-updates
            localBotConfig = JSON.parse(localBotData, (key, value) =>
              value === 'undefined' ? null : value
            );
          }
        }
        const localCurrentBotConfig = localBotConfig?.[key]
          ? localBotConfig[key]
          : undefined;
        set(() => ({
          currentConversation: {
            ...conversation,
            conversationID: id,
          },
          isMultiSession: true,
          currentMultiSession: conversation,
          botConfig: inputs || [],
          botQuestions: questions || [],
          botIntro: intro || '',
          currentBotConfig: localCurrentBotConfig || undefined,
          localBotConfigData: localBotConfig,
        }));
      },
      // getCurrentUserDetail: async (userID: string) => {
      //   if (userID != null && userID !== '') {
      //     try {
      //       const userDetailDataResponse = await IMSDK.getUsersInfo([userID]);
      //       if (userDetailDataResponse?.data?.[0] != null) {
      //         set(() => ({
      //           currentUserDetail: {
      //             ...userDetailDataResponse.data[0],
      //             faceURL: '',
      //             positionInfos:
      //               userDetailDataResponse.data[0].positionInfos || [],
      //           },
      //         }));
      //       } else {
      //         throw new Error(
      //           `接口返回${JSON.stringify(userDetailDataResponse)}`
      //         );
      //       }
      //     } catch (e: any) {
      //       console.error(
      //         `IMSDK.getUsersInfo查询${userID}信息失败,`,
      //         e.message
      //       );
      //       set(() => ({
      //         currentUserDetail: {
      //           userID,
      //           nickname: '--',
      //           faceURL: '',
      //           ex: '',
      //           employeeCode: '',
      //           positionInfos: [],
      //         },
      //       }));
      //     }
      //   } else {
      //     set(() => ({
      //       currentUserDetail: undefined,
      //     }));
      //   }
      // },

      handleConversationClicked: async (contact: any) => {
        const state = get();
        try {
          const draftTextStr =
            state.currentMessageInputValue != null
              ? JSON.stringify(state.currentMessageInputValue)
              : '';

          if (
            state.currentConversation?.conversationID &&
            draftTextStr !== state.currentConversation.draftText
          ) {
            console.debug('更新会话草稿');
            // 内容有变化时才需要重新set
            await IMSDK.setConversationDraft({
              conversationID: state.currentConversation?.conversationID,
              draftText: draftTextStr,
            });

            get().updateConversation({
              ...state.currentConversation,
              draftText: draftTextStr,
              draftTextTime: Date.now(),
            });
          }
        } catch (e) {
          console.error('切换会话，click', e);
        }

        if (state.channelHeaderCurTab !== 'message') {
          get().updateChannelHeaderCurTab('message');
        }
        get().updateCurrentConversation(contact);

        try {
          const resData = await IMSDK.getConversationHasReadAndMaxSeqs([
            contact?.conversationID,
          ]);

          if (contact?.conversationID != null) {
            if (resData.data[0].maxSeq > resData.data[0].hasReadSeq) {
              get().updateReadSeqInfo(
                contact?.conversationID,
                // false,
                resData.data[0].maxSeq,
                resData.data[0].hasReadSeq,
                Array.from(
                  {
                    length: resData.data[0].maxSeq - resData.data[0].hasReadSeq,
                  },
                  (_, i) => resData.data[0].hasReadSeq + i + 1
                ),
                false
              );
            }
          }
        } catch (e) {
          console.error('切换会话', e);
        }
      },
    }),
    {
      name: 'conversationStore',
      enabled: true,
    }
  )
);
