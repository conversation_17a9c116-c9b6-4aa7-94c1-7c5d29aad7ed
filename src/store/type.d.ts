import {
  AtTextElem,
  BlackUserItem,
  ConversationItem,
  FriendApplicationItem,
  FriendUserItem,
  GroupApplicationItem,
  GroupItem,
  GroupMemberItem,
  MessageItem,
  NotificationElem,
  DepartmentItem,
  PublicUserItem,
} from '@ht/openim-wasm-client-sdk/lib/types/entity';

import { UserDetailProps_temp } from '@/pages/contact/components/UserDetail';
import { ChannelHeaderTabType } from '@/components/Channel';
import { SingleTranslation, TranslationState } from '@/store/translationStore';

export type IMConnectState = 'success' | 'loading' | 'failed';
export type CHANGE_RIGHT_ARE_ACTION =
  | 'OPEN_THREAD'
  | 'OPEN_PERSON_DETAIL'
  | 'OPEN_CHANNEL'
  | 'CLEAR_RIGHT_AREA'
  | 'OPEN_RIGHT_AREA'
  | 'OPEN_FILE_AREA'
  | 'OPEN_CHANNEL_HISTORY'
  | 'OPEN_ROBOT_CONVERSATION_AREA'
  | 'OPEN_ROBOT_ANSWER_SOURCE'
  | 'OPEN_ROBOT_ONLINE_SEARCE';

export type StatusType =
  | 'InMeeting' // 会议中
  | 'OnBusinessTrip' // 差旅中
  | 'SickLeave' // 病假
  | 'OnVacation' // 休假
  | 'TemporarilyAway' // 短暂离开
  | 'DoNotDisturb' // 请勿打扰
  | 'OffWork'; // 下班了

export type DurationType =
  | 'Permanent' // 不要清除
  | '30Minutes' // 30分钟
  | '1Hour' // 1小时
  | '4Hours' // 4小时
  | 'Today' // 今天
  | 'ThisWeek' // 本周
  | any; // 自我选择，应该为时间格式

export type UserStatusType = {
  code: StatusType | string;
  desc: string;
  duration: DurationType;
};

export interface UserDetailExProps {
  gender: boolean;
  mail: string;
  mobile: string;
  landline: string;
  employeeCode: string;
  userState: UserStatusType;
}

export interface newVersionType {
  available: boolean;
  data: any;
}

export interface UserStore {
  syncState: IMConnectState;
  progress: number;
  reinstall: boolean;
  isLogining: boolean;
  connectState: IMConnectState;
  selfInfo: SelfUserInfo;
  selfStatus?: UserStatusType;
  appSettings: AppSettings;
  newVersion: newVersionType;
  updateSyncState: (syncState: IMConnectState) => void;
  updateProgressState: (progress: number) => void;
  updateReinstallState: (reinstall: boolean) => void;
  updateIsLogining: (isLogining: boolean) => void;
  updateConnectState: (connectState: IMConnectState) => void;
  updateSelfInfo: (info: Partial<SelfUserInfo>) => void;
  updateSelfState: (ex?: any) => void;
  clearSelfState: (ex?: any) => void;
  getSelfInfoByReq: () => void;
  updateAppSettings: (settings: Partial<AppSettings>) => void;
  userLogout: (force?: boolean) => Promise<void>;
  updateNewVersion: (newVersion: newVersionType) => void;
}

export interface AppSettings {
  locale: LocaleString;
  closeAction: 'miniSize' | 'quit';
}

export type LocaleString = 'zh-CN' | 'en-US';

export type ConversationListUpdateType = 'push' | 'filter';

// 聊天框右侧展示的类型
export type rightAreaType =
  | 'thread'
  | 'personDetail'
  | 'channel'
  | 'file'
  | 'channelHistory'
  | 'channelMemberList'
  | 'robotConversation'
  | 'robotAnswerSource'
  | 'robotOnlineSearch';

export interface BotConfigItem {
  key: string;
  name: string;
  desc: string;
  required: number;
  sortNum: number;
  textMaxLength: number;
  type: 1 | 2 | 3; // 1-文本，2-段落，3-枚举
  enumValues?: string[];
}

export interface CurrentBotConfigType {
  data?: { [key: string]: any };
  config: BotConfigItem[];
}
export interface LocalBotConfigDataType {
  [key: string]: CurrentBotConfigType | null;
}

// 置顶消息详情
export type MessageUpInfo = {
  msgs?: MessageItem;
  upBy?: string;
  upByName?: string;
};

export type rightAreaInGroupListItem = {
  type?: rightAreaType;
  payload?: any;
  key?: string;
};

export interface ConversationStore {
  conversationIniting: boolean;
  conversationList: ConversationItem[];
  currentConversation?: ConversationItem;
  unReadCount: number;
  currentGroupInfo?: GroupItem;
  currentMemberInGroup?: GroupMemberItem;
  rightAreaInGroupList: rightAreaInGroupListItem[];
  rightAreaInGroupConversation?: rightAreaType;
  currentMemberInGroupIniting?: boolean;

  selectedUserID: string; // 右侧选中的用户ID
  currentSidebarMessage?: MessageItem;
  currentSidebarData?: any[];
  currentSidebarActiveObj?: any;
  changeRightArea: (
    changeRightAction: CHANGE_RIGHT_ARE_ACTION,
    payload?: UserDetailProps_temp | [string, string, MessageItem, any[]]
  ) => void;
  checkRightArea: (message: MessageItem) => void;
  changeRightAreaBack: () => void;

  // 当前thread状态（只有thread在会话框里打开时需要读）
  currentThreadConversation?: ConversationItem;
  currentThreadGroupInfo?: GroupItem;
  currentMemberInThreadGroup?: GroupMemberItem;
  currentThreadFirstMessage?: MessageItem; // thread的第一条消息
  // currentUserDetail?: PublicUserItem;
  llmLoading: boolean;
  llmQuestions: {
    questions: string[];
    clientMsgId: string;
  };

  targetMsg: {
    clientMsgID?: string;
    seq?: number;
  };
  channelHeaderCurTab: ChannelHeaderTabType;
  isMultiSession: boolean;
  currentMultiSessionIniting: boolean;
  currentMultiSession?: ConversationItem;
  currentMultiSessionList: ConversationItem[];
  botConfig: BotConfigItem[];
  botConfigModal: boolean;
  localBotConfigData: LocalBotConfigDataType; // 所有机器人会话的配置
  currentBotConfig?: CurrentBotConfigType; // 当前机器人会话的配置
  botQuestions: string[];
  botIntro: string;
  currentReadSeqInfo?: {
    conversationID: string;
    // showBtn: boolean;
    maxSeq: number;
    hasReadSeq: number;
    unReadSeqs: number[];
    clicked: boolean; // 是否已经点击过
  };

  currentMessageUpInfo: MessageUpInfo[];

  removeSingleMsgFromUnReadList: (
    conversationID: string,
    msgSeq: number
  ) => void;
  clearReadSeqInfo: () => void;

  hideReadSeqInfo: () => void;
  updateReadSeqInfo: (
    conversationID: string,
    maxSeq: number,
    hasReadSeq: number,
    unReadSeqs: number[],
    clicked: boolean // 是否已经点击过
  ) => void;

  updateTargetMsg: (msgInfo: { clientMsgID?: string; seq?: number }) => void;
  updateCurrentMemberInGroupIniting: (val: boolean) => void;

  clearCurrentThreadConversation: () => void; // 清空右侧的当前子会话

  getCurrentThreadGroupInfoByReq: (
    groupID: string,
    parentId: string
  ) => Promise<void>;
  updateCurrentThreadGroupInfo: (
    groupInfo: GroupItem,
    parentId: string
  ) => void;
  getCurrentMemberInThreadGroupByReq: (
    groupID: string,
    parentId: string
  ) => Promise<void>;
  setCurrentMemberInThreadGroup: (
    memberInfo?: GroupMemberItem,
    parentId: string
  ) => void;
  tryUpdateCurrentMemberInThreadGroup: (
    member: GroupMemberItem,
    parentId: string
  ) => void;

  getConversationListByReq: (
    isOffset?: boolean,
    forceLoadin?: boolean
  ) => Promise<boolean>;
  updateConversationList: (
    list: ConversationItem[],
    type: ConversationListUpdateType
  ) => void;
  delConversationByCID: (conversationID: string) => void;
  // getCurrentConversationByReq: (conversationID?: string) => Promise<void>;
  updateCurrentConversation: (
    conversation?: ConversationItem,
    isJump?: boolean
  ) => Promise<void>;

  updateConversation: (newConversationItem?: ConversationItem) => any;

  openThreadConversation: (threadID: string, message: MessageItem) => void;
  setCurrentThreadConversation: (
    threadID: string,
    parentID: string,
    message: MessageItem
  ) => Promise<void>;
  updateCurrentThreadConversation: (
    conversation?: ConversationItem,
    isJump?: boolean
  ) => Promise<void>;
  updateCurrentConversationFields: (fields: Partial<ConversationItem>) => void;
  getUnReadCountByReq: () => Promise<number>;
  updateUnReadCount: (count: number) => void;
  getCurrentGroupInfoByReq: (groupID: string) => Promise<void>;
  updateCurrentGroupInfo: (groupInfo: GroupItem) => void;
  getCurrentMemberInGroupByReq: (groupID: string) => Promise<void>;
  getCurrentMessageUpInfo: (groupID?: string) => Promise<void>;
  setCurrentMemberInGroup: (memberInfo?: GroupMemberItem) => void;
  tryUpdateCurrentMemberInGroup: (member: GroupMemberItem) => void;
  clearConversationStore: () => void;
  updateChannelHeaderCurTab: (val: ChannelHeaderTabType) => void;
  updateChannelHeaderCurTabAndConversation: (
    val: ChannelHeaderTabType,
    msgInfo: { clientMsgID?: string; seq?: number },
    conversation: ConversationItem
  ) => void;
  // getCurrentUserDetail: (userID: string) => void;
  updateLlmLoading: (val: boolean) => void;
  updateLlmQuestions: (val: string[], clientMsgId: string) => void;
  clearConversationUnReadCount: (conversation: ConversationItem) => void;

  // currentMessageInputFunc?: () => string;
  // setGetCurrentMessageInputFunc: (prop: () => string) => void;

  currentMessageInputValue?: any;
  setCurrentMessageInputValue: any;
  getCurrentMessageInputValue: any;
  checkIsMultiSession: (conversation: ConversationItem) => Promise<boolean>;
  getCurrentMultiSessionList: (conversationID: string) => Promise<void>;
  updateCurrentMultiSession: (
    conversation?: ConversationItem,
    isJump?: boolean
  ) => void;
  updateRobotConfig: (val: CurrentBotConfigType) => void;
  createCurrentMultiSession: () => void;
  updateBotConfigModal: (open: boolean, type?: 'default' | 'reset') => void;
  initCurrentMultiSession: (conversation: ConversationItem) => Promise<void>;
  updateMultiSessionForSearch: (conversation: ConversationItem) => void;

  handleConversationClicked: (contact: any) => Promise<void>;
}

export interface ContactStore {
  contachIniting: boolean;
  friendList: FriendUserItem[];
  blackList: BlackUserItem[];
  groupList: GroupItem[];
  recvFriendApplicationList: FriendApplicationItem[];
  sendFriendApplicationList: FriendApplicationItem[];
  recvGroupApplicationList: GroupApplicationItem[];
  sendGroupApplicationList: GroupApplicationItem[];
  unHandleFriendApplicationCount: number;
  unHandleGroupApplicationCount: number;
  getFriendListByReq: () => Promise<void>;
  setFriendList: (list: FriendUserItem[]) => void;
  updateFriend: (friend: FriendUserItem, remove?: boolean) => void;
  pushNewFriend: (friend: FriendUserItem) => void;
  getBlackListByReq: () => Promise<void>;
  updateBlack: (black: BlackUserItem, remove?: boolean) => void;
  pushNewBlack: (black: BlackUserItem) => void;
  getGroupListByReq: () => Promise<void>;
  setGroupList: (list: GroupItem[]) => void;
  updateGroup: (group: GroupItem, remove?: boolean) => void;
  pushNewGroup: (group: GroupItem) => void;
  getRecvFriendApplicationListByReq: () => Promise<void>;
  updateRecvFriendApplication: (
    application: FriendApplicationItem
  ) => Promise<void>;
  getSendFriendApplicationListByReq: () => Promise<void>;
  updateSendFriendApplication: (application: FriendApplicationItem) => void;
  getRecvGroupApplicationListByReq: () => Promise<void>;
  updateRecvGroupApplication: (
    application: GroupApplicationItem
  ) => Promise<void>;
  getSendGroupApplicationListByReq: () => Promise<void>;
  updateSendGroupApplication: (application: GroupApplicationItem) => void;
  updateUnHandleFriendApplicationCount: (num: number) => void;
  updateUnHandleGroupApplicationCount: (num: number) => void;
  clearContactStore: () => void;
}

export interface DepartmentInfoType {
  departmentInfoIniting?: boolean;
  departmentInfo: DepartmentItem[];
  startSyncDepartmentInfo: () => void;
  endSyncDepartmentInfo: () => void;
  initDepartmentInfo: () => void;
}

export interface SearchInfoType {
  globalSearchModalVisible: boolean;
  searchValue: string;
  searchConversationInfo: ConversationItem | null;
  needShowRightArea: boolean;
  rightAreaInSearchList: rightAreaInGroupListItem[];
  rightAreaInSearch?: rightAreaType | null;
  rightPayload?: UserDetailProps_temp | [string, string, MessageItem];
  updateSearchInfo: (fields: Partial<SearchInfoType>) => void;
  changeRightArea: (
    changeRightAction: CHANGE_RIGHT_ARE_ACTION,
    payload?: UserDetailProps_temp | [string, string, MessageItem]
  ) => void;
  changeRightAreaBack: () => void;
}

export interface TranslationStore {
  translations: Record<string, SingleTranslation>;
  setTranslation: (clientMsgID: string, content: string) => void;
  updateTranslatedContent: (clientMsgID: string, content: string) => void;
  updateTranslationState: (
    clientMsgID: string,
    state: TranslationState
  ) => void;
  clearTranslation: (clientMsgID: string) => void;
  clearAllTranslations: () => void;
}

export interface GlobalModalStore {
  isStatusModalOpen: boolean; // 是否打开用户状态的弹框
  isEditModalOpen: boolean; // 是否打开设置用户状态的弹框
  isNetErrorTooltipOpen: boolean; // 是否显示右上角网络异常提示

  setStatusModalOpen: (open: boolean) => void;
  setEditModalOpen: (open: boolean) => void;
  setNetErrorTooltipOpen: (open: boolean) => void;
}
