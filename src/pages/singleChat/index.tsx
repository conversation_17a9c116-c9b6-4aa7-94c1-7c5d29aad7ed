import { SessionType } from '@ht/openim-wasm-client-sdk';
import { useEffect, useState } from 'react';
import Channel from '@/components/Channel';
import { getPageQuery } from '@/utils/utils';
import { useConversationToggle } from '@/hooks/useConversationToggle';
import { IMSDK } from '@/layouts/BasicLayout';
import { Spin } from '@ht/sprite-ui';
import singleChatNoPermissionIcon from '@/assets/singleChatNoPermission.png';
import styles from './index.less';

type ChatType = 'user' | 'group';

const SingleChat = () => {
  const TypeList = ['user', 'group'];
  const urlQuery: {
    type?: ChatType;
    chatId?: string;
  } = getPageQuery();
  const { type = '', chatId = '' } = urlQuery;
  const [visibility, setVisibility] = useState(true);
  const [loading, setLoading] = useState(true);
  // chatId：type字段为user类型的，chatId对应聊天对端的用户id；type字段为group类型的，chatId对应聊天的conversationid；

  const { toSpecifiedConversation } = useConversationToggle();

  const handleCheckEmployeeVisibility = async () => {
    try {
      const res = await IMSDK.checkEmployeeVisibility(chatId);
      if (res.data) {
        handleConversation();
      } else {
        setVisibility(false);
      }
    } catch (err) {
      setVisibility(false);
      setLoading(false);
    }
  };

  const handleConversation = async () => {
    if (chatId && TypeList.includes(type)) {
      toSpecifiedConversation(
        {
          sourceID: chatId,
          sessionType:
            type === 'group' ? SessionType.Group : SessionType.Single,
        },
        false
      );
      setLoading(false);
    }
  };

  useEffect(() => {
    handleCheckEmployeeVisibility();
  }, []);

  return (
    <div className={styles.singleChatWrapper}>
      {loading && (
        <div className={styles.spinWrapper}>
          <Spin type="arc" size="large" className={styles.singleSpin} />
        </div>
      )}
      {visibility ? (
        <>
          <div className={styles.singleThemeBg}></div>
          <div className={styles.singleChatContent}>
            <Channel isSingleChat={true} />
          </div>
        </>
      ) : (
        <div className={styles.noPermission}>
          <img src={singleChatNoPermissionIcon} />
          <span className={styles.noPermissionDesc}>会话无访问权限</span>
        </div>
      )}
    </div>
  );
};

export default SingleChat;
