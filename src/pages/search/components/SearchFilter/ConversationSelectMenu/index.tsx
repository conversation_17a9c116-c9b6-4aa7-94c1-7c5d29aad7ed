import classNames from 'classnames';
import { FC, useEffect, useState, useMemo } from 'react';
import { Typography } from '@ht/sprite-ui';
import { ConversationItem } from '@ht/openim-wasm-client-sdk/lib/types/entity';
import { isEmpty } from 'lodash';
import Highlighter from 'react-highlight-words';
import { IMSDK } from '@/layouts/BasicLayout';
import { Virtuoso } from '@ht/react-virtuoso';
import OIMAvatar from '@/components/OIMAvatar';
import GroupAvatar from '@/components/ChannelList/GroupAvatar';
import checkIcon from '@/assets/images/searchFilter/check.png';
import checkedIcon from '@/assets/images/searchFilter/checked.svg';
import deleteIcon from '@/assets/images/searchFilter/delete.svg';
import groupIcon from '@/assets/coversation/group.svg';
import FilterSearchInput from '../FilterSearchInput';
import SelectSearchEmpty from '../SelectSearchEmpty';
import styles from './index.less';

interface ConversationSelectMenuProps {
  handleClose: () => void;
  onSearchFilterParamsChange: (params: any) => void;
  searchFilterParams: any;
}

const PageSize = 20;

const ConversationSelectMenu: FC<ConversationSelectMenuProps> = ({
  onSearchFilterParamsChange,
  handleClose,
  searchFilterParams,
}) => {
  const { conversationSelect = [] } = searchFilterParams;
  const [searchValue, setSearchValue] = useState('');
  const [searchInfo, setSearchInfo] = useState<{
    list: ConversationItem[];
    loading: boolean;
    page: number;
    isEnd: boolean;
  }>({
    list: [],
    loading: false,
    page: 0,
    isEnd: false,
  });
  useEffect(() => {
    handleConversationSearch();
  }, [searchValue]);

  const handleConversationSearch = async (page = 1) => {
    if (page === 1) {
      setSearchInfo((pre) => {
        return { ...pre, loading: true };
      });
    }
    try {
      const { data } = await IMSDK.searchConversationsSplit({
        searchParam: searchValue,
        offset: (page - 1) * PageSize,
        count: PageSize,
      });
      console.warn(data, 'searchConversationsSplit');
      const currentList = data || [];
      setSearchInfo((pre) => {
        return {
          list: page === 1 ? currentList : [...pre.list, ...currentList],
          page,
          loading: false,
          isEnd: currentList.length < PageSize,
        };
      });
    } catch (e) {
      setSearchInfo({
        list: [],
        page: 1,
        loading: false,
        isEnd: false,
      });
    }
  };

  const currentSelectList = useMemo(() => {
    return searchInfo.list.filter((item) => {
      return (
        conversationSelect.findIndex(
          (i: ConversationItem) => item.conversationID === i.conversationID
        ) === -1
      );
    }, []);
  }, [searchInfo.list, conversationSelect]);

  // 是否展示列表
  const ifShowList = useMemo(() => {
    //   搜索数据全部选中后不展示
    return (
      currentSelectList.filter((item) => {
        return (
          conversationSelect.findIndex(
            (i: ConversationItem) => i.conversationID === item.conversationID
          ) === -1
        );
      })?.length > 0 ||
      (searchInfo.list.length === 0 && !isEmpty(searchValue))
    );
  }, [
    currentSelectList,
    searchInfo.list.length,
    searchValue,
    conversationSelect,
  ]);

  const virtuosoStyle = useMemo(() => {
    const allLength = currentSelectList.length + conversationSelect.length;
    const delHeight = conversationSelect.length > 0 ? '32px' : '0px';
    const emptyHeight = ifShowList ? '300px' : '0px';
    if (currentSelectList.length > 0) {
      return { height: `calc(52px * ${allLength} + ${delHeight})` };
    } else {
      return {
        height: `calc(${emptyHeight} + 52px * ${conversationSelect.length} + ${delHeight})`,
      };
    }
  }, [currentSelectList, conversationSelect, ifShowList]);

  const handleUncheck = (selectItem: ConversationItem) => {
    const newList = conversationSelect.filter(
      (i: ConversationItem) => i.conversationID !== selectItem.conversationID
    );
    onSearchFilterParamsChange({
      conversationSelect: newList,
    });
  };

  const renderAvatar = (searchItem: ConversationItem) => {
    return searchItem?.userID == null || searchItem?.userID === '' ? (
      <GroupAvatar
        faceMember={searchItem?.faceMember}
        size={'little'}
        style={{ marginRight: 10 }}
      />
    ) : (
      <OIMAvatar
        size={32}
        userID={searchItem.userID}
        hideOnlineStatus={true}
        style={{ marginRight: '10px' }}
      />
    );
  };
  return (
    <div
      className={styles.conversationSelectMenuWrapper}
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
      }}
    >
      <div style={{ margin: '6px 10px' }}>
        <FilterSearchInput
          placeholder="输入名称找会话"
          onInputChange={(val: string) => setSearchValue(val)}
        />
      </div>
      <div
        style={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <Virtuoso
          className={styles.virtuosoListContainer}
          style={virtuosoStyle}
          data={ifShowList ? currentSelectList : []}
          itemContent={(index: number, searchItem: ConversationItem) => {
            return (
              <div
                key={searchItem.conversationID}
                className={classNames(styles.menuItemButton)}
                onClick={() => {
                  onSearchFilterParamsChange({
                    conversationSelect: [...conversationSelect, searchItem],
                  });
                }}
              >
                <img src={checkIcon} className={styles.checkIcon} />
                {renderAvatar(searchItem)}
                <Typography.Text
                  ellipsis={true}
                  title={searchItem.showName}
                  style={{ maxWidth: 150 }}
                >
                  <Highlighter
                    highlightClassName={styles.highlightClassName}
                    searchWords={[searchValue]}
                    autoEscape={true}
                    textToHighlight={searchItem.showName}
                  />
                </Typography.Text>
              </div>
            );
          }}
          endReached={() => {
            if (!searchInfo.isEnd) {
              handleConversationSearch(searchInfo.page + 1);
            }
          }}
          increaseViewportBy={500}
          components={{
            EmptyPlaceholder: () => {
              return ifShowList ? <SelectSearchEmpty /> : <></>;
            },
            Header: () => {
              return (
                <>
                  {conversationSelect.length > 0 && (
                    <div className={styles.selectedList}>
                      <div className={styles.selectedDesc}>
                        <span>已选记录</span>
                        <span
                          className={styles.deleteAll}
                          onClick={() => {
                            onSearchFilterParamsChange({
                              conversationSelect: [],
                            });
                          }}
                        >
                          全部删除
                        </span>
                      </div>
                      {conversationSelect.map(
                        (selectItem: ConversationItem) => {
                          return (
                            <div
                              key={selectItem.conversationID}
                              className={classNames(styles.menuItemButton)}
                            >
                              <div className={styles.searchValue}>
                                <img
                                  src={checkedIcon}
                                  className={styles.checkIcon}
                                  onClick={() => handleUncheck(selectItem)}
                                />
                                {renderAvatar(selectItem)}
                                <Typography.Text
                                  ellipsis={true}
                                  title={selectItem.showName}
                                  style={{ maxWidth: 120 }}
                                >
                                  {selectItem.showName}
                                </Typography.Text>
                              </div>
                              <div
                                className={styles.deleteIcon}
                                onClick={() => handleUncheck(selectItem)}
                              >
                                <img src={deleteIcon} />
                              </div>
                            </div>
                          );
                        }
                      )}
                    </div>
                  )}
                  {conversationSelect.length > 0 && ifShowList && (
                    <div className={styles.line}></div>
                  )}
                </>
              );
            },
          }}
        />
      </div>
    </div>
  );
};
export default ConversationSelectMenu;
