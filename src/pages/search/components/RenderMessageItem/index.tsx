import { FC } from 'react';
import {
  MessageItem as MessageItemType,
  SearchMessageResultItem,
  SearchServerMsgItem,
  ConversationItem,
} from '@ht/openim-wasm-client-sdk';
import { IMSDK } from '@/layouts/BasicLayout';
import { useConversationStore, useSearchInfoStore } from '@/store';
import MessageItem from '@/components/Channel/components/MessageItem';
import styles from './index.less';

type SearchListType =
  | (MessageItemType & SearchMessageResultItem)
  | SearchServerMsgItem;

interface RenderMessageItemProps {
  messagetItem: SearchListType;
  searchValue: string;
}

const RenderMessageItem: FC<RenderMessageItemProps> = ({
  messagetItem,
  searchValue,
}) => {
  const { updateCurrentConversation, updateMultiSessionForSearch } =
    useConversationStore((state) => ({
      updateCurrentConversation: state.updateCurrentConversation,
      updateMultiSessionForSearch: state.updateMultiSessionForSearch,
    }));
  const { changeRightArea } = useSearchInfoStore();

  const updateConversation = (conversation: ConversationItem) => {
    if (conversation?.parentId) {
      updateMultiSessionForSearch(conversation);
    } else {
      updateCurrentConversation(conversation);
    }
  };

  const onSearchItemClick = async (searchItem: SearchListType) => {
    // 点击切换聊天会话的定位消息的处理放在index里监听rightAreaInSearch === 'channel'
    const conversation = await IMSDK.getMultipleConversation([
      searchItem.conversationID,
    ]);

    setTimeout(() => {
      const curConversation = {
        ...conversation.data[0],
        conversationID: searchItem.conversationID,
      };
      changeRightArea('OPEN_CHANNEL', {
        conversation: curConversation,
        searchItem,
      });
    }, 500);
  };

  return (
    <div
      className={styles.messageItemWrapper}
      key={messagetItem.clientMsgID}
      onClick={() => {
        onSearchItemClick(messagetItem);
      }}
    >
      <div className={styles.showName}>{messagetItem.showName}</div>
      <div className={styles.messageWrapper}>
        <MessageItem
          isThread={false}
          conversationID={messagetItem.conversationID}
          message={messagetItem}
          messageUpdateFlag={
            messagetItem.senderNickname + messagetItem.senderFaceUrl
          }
          isSender={false}
          showName={messagetItem?.showName}
          disabled={true}
          isSearch={true}
          searchValue={searchValue}
        />
      </div>
    </div>
  );
};

export default RenderMessageItem;
