import withScrollBar from '@/components/withScrollBar';
import { useConversationStore } from '@/store';
import UserDetail from '../UserDetail';
import styles from './index.less';

const RightUserDetail = () => {
  const { selectedUserID, changeRightArea } = useConversationStore((state) => ({
    selectedUserID: state.selectedUserID,
    changeRightArea: state.changeRightArea,
  }));
  return (
    <div className={styles.rightArea}>
      <UserDetail
        userID={selectedUserID}
        handleSelectedUserClear={() => {
          changeRightArea('CLEAR_RIGHT_AREA');
        }}
      />
    </div>
  );
};

export default withScrollBar(RightUserDetail, {
  domId: 'drag-scroll-bar-right-userDetail',
  direction: 'right',
  defaultWidth: '510px',
  dragBarClassName: 'scrollbarRight',
  minWidth: 290,
  maxWidth: 570,
});
